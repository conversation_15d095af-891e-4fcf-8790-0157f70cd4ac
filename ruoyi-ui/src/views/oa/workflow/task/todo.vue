<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程名称" prop="workflowName">
        <el-input
          v-model="queryParams.workflowName"
          placeholder="请输入流程名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskList">
      <el-table-column label="任务ID" align="center" prop="taskId" />
      <el-table-column label="任务名称" align="center" prop="taskName" />
      <el-table-column label="流程名称" align="center" prop="workflowName" />
      <el-table-column label="流程实例ID" align="center" prop="processInstanceId" />
      <el-table-column label="任务创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleTask(scope.row)"
          >处理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-share"
            @click="handleDelegate(scope.row)"
          >转办</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 任务处理对话框 -->
    <el-dialog :title="taskTitle" :visible.sync="taskOpen" width="800px" append-to-body>
      <el-form ref="taskForm" :model="taskForm" label-width="100px">
        <el-form-item label="任务名称">
          <el-input v-model="taskForm.taskName" readonly />
        </el-form-item>
        <el-form-item label="流程名称">
          <el-input v-model="taskForm.workflowName" readonly />
        </el-form-item>
        <el-form-item label="审批意见" prop="comment">
          <el-input v-model="taskForm.comment" type="textarea" :rows="4" placeholder="请输入审批意见" />
        </el-form-item>
        <el-form-item label="审批结果" prop="result">
          <el-radio-group v-model="taskForm.result">
            <el-radio label="agree">同意</el-radio>
            <el-radio label="reject">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTask">提 交</el-button>
        <el-button @click="cancelTask">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 转办对话框 -->
    <el-dialog title="任务转办" :visible.sync="delegateOpen" width="500px" append-to-body>
      <el-form ref="delegateForm" :model="delegateForm" label-width="100px">
        <el-form-item label="转办给" prop="assignee">
          <el-select v-model="delegateForm.assignee" placeholder="请选择转办人" style="width: 100%">
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.nickName"
              :value="user.userName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="转办原因" prop="reason">
          <el-input v-model="delegateForm.reason" type="textarea" :rows="3" placeholder="请输入转办原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDelegate">确 定</el-button>
        <el-button @click="cancelDelegate">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTodoTasks, completeTask, delegateTask } from "@/api/oa/workflow";
import { listUser } from "@/api/system/user";

export default {
  name: "TodoTask",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 待办任务表格数据
      taskList: [],
      // 用户列表
      userList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        workflowName: null
      },
      // 任务处理表单
      taskForm: {},
      // 任务处理对话框
      taskOpen: false,
      taskTitle: "",
      // 转办表单
      delegateForm: {},
      // 转办对话框
      delegateOpen: false
    };
  },
  created() {
    this.getList();
    this.getUserList();
  },
  methods: {
    /** 查询待办任务列表 */
    getList() {
      this.loading = true;
      getTodoTasks(this.queryParams).then(response => {
        this.taskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询用户列表 */
    getUserList() {
      listUser().then(response => {
        this.userList = response.rows;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 处理任务 */
    handleTask(row) {
      this.taskForm = {
        taskId: row.taskId,
        taskName: row.taskName,
        workflowName: row.workflowName,
        processInstanceId: row.processInstanceId,
        comment: "",
        result: "agree"
      };
      this.taskOpen = true;
      this.taskTitle = "处理任务：" + row.taskName;
    },
    /** 查看任务 */
    handleView(row) {
      this.$router.push({
        path: '/oa/workflow/task/view',
        query: { taskId: row.taskId, processInstanceId: row.processInstanceId }
      });
    },
    /** 转办任务 */
    handleDelegate(row) {
      this.delegateForm = {
        taskId: row.taskId,
        assignee: "",
        reason: ""
      };
      this.delegateOpen = true;
    },
    /** 提交任务处理 */
    submitTask() {
      this.$refs["taskForm"].validate(valid => {
        if (valid) {
          completeTask(this.taskForm.taskId, this.taskForm).then(response => {
            this.$modal.msgSuccess("任务处理成功");
            this.taskOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消任务处理 */
    cancelTask() {
      this.taskOpen = false;
      this.taskForm = {};
    },
    /** 提交转办 */
    submitDelegate() {
      this.$refs["delegateForm"].validate(valid => {
        if (valid) {
          delegateTask(this.delegateForm.taskId, this.delegateForm).then(response => {
            this.$modal.msgSuccess("任务转办成功");
            this.delegateOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消转办 */
    cancelDelegate() {
      this.delegateOpen = false;
      this.delegateForm = {};
    }
  }
};
</script>
