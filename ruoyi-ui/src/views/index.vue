<template>
  <div class="oa-dashboard">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1>欢迎使用OA办公系统</h1>
          <p>{{ currentTime }} | {{ userName }}，您好！</p>
        </div>
        <div class="welcome-icon">
          <i class="el-icon-office-building"></i>
        </div>
      </div>
    </div>

    <!-- 快捷功能区 -->
    <el-row :gutter="20" class="quick-actions">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="quick-card" @click="goToWorkflow">
          <div class="quick-icon workflow">
            <i class="el-icon-document"></i>
          </div>
          <div class="quick-text">
            <h3>工作流程</h3>
            <p>流程审批</p>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="quick-card" @click="goToDocument">
          <div class="quick-icon document">
            <i class="el-icon-folder"></i>
          </div>
          <div class="quick-text">
            <h3>文档管理</h3>
            <p>文件共享</p>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="quick-card" @click="goToSchedule">
          <div class="quick-icon schedule">
            <i class="el-icon-date"></i>
          </div>
          <div class="quick-text">
            <h3>日程安排</h3>
            <p>时间管理</p>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="quick-card" @click="goToNotice">
          <div class="quick-icon notice">
            <i class="el-icon-bell"></i>
          </div>
          <div class="quick-text">
            <h3>通知公告</h3>
            <p>消息中心</p>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 工作台内容区 -->
    <el-row :gutter="20" class="dashboard-content">
      <!-- 待办事项 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-s-order"></i> 待办事项</span>
            <el-button style="float: right; padding: 3px 0" type="text">更多</el-button>
          </div>
          <div class="todo-list">
            <div class="todo-item" v-for="(item, index) in todoList" :key="index">
              <div class="todo-content">
                <h4>{{ item.title }}</h4>
                <p>{{ item.description }}</p>
                <span class="todo-time">{{ item.time }}</span>
              </div>
              <div class="todo-status" :class="item.priority">
                {{ item.status }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 最新通知 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="8">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-message-solid"></i> 最新通知</span>
            <el-button style="float: right; padding: 3px 0" type="text">更多</el-button>
          </div>
          <div class="notice-list">
            <div class="notice-item" v-for="(notice, index) in noticeList" :key="index">
              <div class="notice-content">
                <h4>{{ notice.title }}</h4>
                <p>{{ notice.content }}</p>
                <span class="notice-time">{{ notice.time }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 工作统计 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
        <el-card class="dashboard-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-data-analysis"></i> 工作统计</span>
          </div>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">{{ workStats.pending }}</div>
              <div class="stat-label">待处理</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ workStats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ workStats.documents }}</div>
              <div class="stat-label">文档数</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ workStats.meetings }}</div>
              <div class="stat-label">会议数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "Index",
  data() {
    return {
      currentTime: '',
      userName: '用户',
      todoList: [
        {
          title: '项目方案审批',
          description: '需要审批新项目的实施方案',
          time: '2小时前',
          status: '待处理',
          priority: 'high'
        },
        {
          title: '月度报告提交',
          description: '提交本月工作总结报告',
          time: '1天前',
          status: '进行中',
          priority: 'medium'
        },
        {
          title: '团队会议安排',
          description: '安排下周团队例会时间',
          time: '2天前',
          status: '待确认',
          priority: 'low'
        }
      ],
      noticeList: [
        {
          title: '系统维护通知',
          content: '系统将于本周六进行例行维护',
          time: '1小时前'
        },
        {
          title: '新员工入职培训',
          content: '下周一开始新员工入职培训',
          time: '3小时前'
        },
        {
          title: '假期安排通知',
          content: '国庆节假期安排已发布',
          time: '1天前'
        }
      ],
      workStats: {
        pending: 12,
        completed: 45,
        documents: 128,
        meetings: 8
      }
    };
  },
  mounted() {
    this.updateTime();
    this.getUserInfo();
    // 每分钟更新一次时间
    setInterval(this.updateTime, 60000);
  },
  methods: {
    updateTime() {
      const now = new Date();
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long',
        hour: '2-digit',
        minute: '2-digit'
      };
      this.currentTime = now.toLocaleDateString('zh-CN', options);
    },
    getUserInfo() {
      // 获取用户信息
      const userInfo = this.$store.state.user.name;
      if (userInfo) {
        this.userName = userInfo;
      }
    },
    goToWorkflow() {
      this.$router.push('/oa/workflow');
    },
    goToDocument() {
      this.$router.push('/oa/document');
    },
    goToSchedule() {
      this.$router.push('/oa/schedule');
    },
    goToNotice() {
      this.$router.push('/system/notice');
    }
  }
};
</script>

<style scoped lang="scss">
.oa-dashboard {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);

  // 欢迎横幅
  .welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 20px;
    color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    .welcome-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .welcome-text {
        h1 {
          margin: 0 0 10px 0;
          font-size: 28px;
          font-weight: 600;
        }
        p {
          margin: 0;
          font-size: 16px;
          opacity: 0.9;
        }
      }

      .welcome-icon {
        font-size: 60px;
        opacity: 0.3;
      }
    }
  }

  // 快捷功能区
  .quick-actions {
    margin-bottom: 20px;

    .quick-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .quick-icon {
        font-size: 36px;
        margin-bottom: 10px;

        &.workflow { color: #409eff; }
        &.document { color: #67c23a; }
        &.schedule { color: #e6a23c; }
        &.notice { color: #f56c6c; }
      }

      .quick-text {
        h3 {
          margin: 0 0 5px 0;
          font-size: 16px;
          color: #303133;
        }
        p {
          margin: 0;
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  // 工作台内容区
  .dashboard-content {
    .dashboard-card {
      margin-bottom: 20px;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
        color: #303133;

        i {
          margin-right: 8px;
          color: #409eff;
        }
      }
    }
  }

  // 待办事项
  .todo-list {
    .todo-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .todo-content {
        flex: 1;

        h4 {
          margin: 0 0 5px 0;
          font-size: 14px;
          color: #303133;
        }
        p {
          margin: 0 0 5px 0;
          font-size: 12px;
          color: #909399;
        }
        .todo-time {
          font-size: 11px;
          color: #c0c4cc;
        }
      }

      .todo-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;

        &.high {
          background: #fef0f0;
          color: #f56c6c;
        }
        &.medium {
          background: #fdf6ec;
          color: #e6a23c;
        }
        &.low {
          background: #f0f9ff;
          color: #409eff;
        }
      }
    }
  }

  // 通知列表
  .notice-list {
    .notice-item {
      padding: 15px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .notice-content {
        h4 {
          margin: 0 0 5px 0;
          font-size: 14px;
          color: #303133;
        }
        p {
          margin: 0 0 5px 0;
          font-size: 12px;
          color: #909399;
        }
        .notice-time {
          font-size: 11px;
          color: #c0c4cc;
        }
      }
    }
  }

  // 工作统计
  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    .stat-item {
      text-align: center;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;

      .stat-number {
        font-size: 24px;
        font-weight: 600;
        color: #409eff;
        margin-bottom: 5px;
      }

      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .oa-dashboard {
    padding: 10px;

    .welcome-banner {
      padding: 20px;

      .welcome-content {
        flex-direction: column;
        text-align: center;

        .welcome-icon {
          margin-top: 10px;
          font-size: 40px;
        }
      }
    }

    .quick-actions .quick-card {
      height: 100px;
      padding: 15px;

      .quick-icon {
        font-size: 28px;
        margin-bottom: 5px;
      }

      .quick-text h3 {
        font-size: 14px;
      }
    }
  }
}
</style>

