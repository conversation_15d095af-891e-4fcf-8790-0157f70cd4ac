package com.base.oa.seal.mapper;

import java.util.List;
import com.base.oa.seal.domain.OaSealApplication;

/**
 * 印章申请Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OaSealApplicationMapper 
{
    /**
     * 查询印章申请
     * 
     * @param applicationId 印章申请主键
     * @return 印章申请
     */
    public OaSealApplication selectOaSealApplicationByApplicationId(Long applicationId);

    /**
     * 查询印章申请列表
     * 
     * @param oaSealApplication 印章申请
     * @return 印章申请集合
     */
    public List<OaSealApplication> selectOaSealApplicationList(OaSealApplication oaSealApplication);

    /**
     * 新增印章申请
     * 
     * @param oaSealApplication 印章申请
     * @return 结果
     */
    public int insertOaSealApplication(OaSealApplication oaSealApplication);

    /**
     * 修改印章申请
     * 
     * @param oaSealApplication 印章申请
     * @return 结果
     */
    public int updateOaSealApplication(OaSealApplication oaSealApplication);

    /**
     * 删除印章申请
     * 
     * @param applicationId 印章申请主键
     * @return 结果
     */
    public int deleteOaSealApplicationByApplicationId(Long applicationId);

    /**
     * 批量删除印章申请
     * 
     * @param applicationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaSealApplicationByApplicationIds(Long[] applicationIds);
}
