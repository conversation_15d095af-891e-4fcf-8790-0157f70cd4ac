package com.base.oa.seal.mapper;

import java.util.List;
import com.base.oa.seal.domain.OaSeal;

/**
 * 印章管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OaSealMapper 
{
    /**
     * 查询印章管理
     * 
     * @param sealId 印章管理主键
     * @return 印章管理
     */
    public OaSeal selectOaSealBySealId(Long sealId);

    /**
     * 查询印章管理列表
     * 
     * @param oaSeal 印章管理
     * @return 印章管理集合
     */
    public List<OaSeal> selectOaSealList(OaSeal oaSeal);

    /**
     * 新增印章管理
     * 
     * @param oaSeal 印章管理
     * @return 结果
     */
    public int insertOaSeal(OaSeal oaSeal);

    /**
     * 修改印章管理
     * 
     * @param oaSeal 印章管理
     * @return 结果
     */
    public int updateOaSeal(OaSeal oaSeal);

    /**
     * 删除印章管理
     * 
     * @param sealId 印章管理主键
     * @return 结果
     */
    public int deleteOaSealBySealId(Long sealId);

    /**
     * 批量删除印章管理
     * 
     * @param sealIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaSealBySealIds(Long[] sealIds);
}
