package com.base.oa.seal.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;

/**
 * 印章管理对象 oa_seal
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class OaSeal extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 印章ID */
    private Long sealId;

    /** 印章名称 */
    @Excel(name = "印章名称")
    private String sealName;

    /** 印章类型 */
    @Excel(name = "印章类型")
    private String sealType;

    /** 印章编号 */
    @Excel(name = "印章编号")
    private String sealNumber;

    /** 印章图片 */
    private String sealImage;

    /** 管理员ID */
    private Long managerId;

    /** 管理员姓名 */
    @Excel(name = "管理员姓名")
    private String managerName;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 启用日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "启用日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date enableDate;

    /** 停用日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "停用日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date disableDate;

    /** 使用次数 */
    @Excel(name = "使用次数")
    private Long useCount;

    /** 最后使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后使用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastUseTime;

    /** 描述 */
    private String description;

    public void setSealId(Long sealId) 
    {
        this.sealId = sealId;
    }

    public Long getSealId() 
    {
        return sealId;
    }
    public void setSealName(String sealName) 
    {
        this.sealName = sealName;
    }

    public String getSealName() 
    {
        return sealName;
    }
    public void setSealType(String sealType) 
    {
        this.sealType = sealType;
    }

    public String getSealType() 
    {
        return sealType;
    }
    public void setSealNumber(String sealNumber) 
    {
        this.sealNumber = sealNumber;
    }

    public String getSealNumber() 
    {
        return sealNumber;
    }
    public void setSealImage(String sealImage) 
    {
        this.sealImage = sealImage;
    }

    public String getSealImage() 
    {
        return sealImage;
    }
    public void setManagerId(Long managerId) 
    {
        this.managerId = managerId;
    }

    public Long getManagerId() 
    {
        return managerId;
    }
    public void setManagerName(String managerName) 
    {
        this.managerName = managerName;
    }

    public String getManagerName() 
    {
        return managerName;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setEnableDate(Date enableDate) 
    {
        this.enableDate = enableDate;
    }

    public Date getEnableDate() 
    {
        return enableDate;
    }
    public void setDisableDate(Date disableDate) 
    {
        this.disableDate = disableDate;
    }

    public Date getDisableDate() 
    {
        return disableDate;
    }
    public void setUseCount(Long useCount) 
    {
        this.useCount = useCount;
    }

    public Long getUseCount() 
    {
        return useCount;
    }
    public void setLastUseTime(Date lastUseTime) 
    {
        this.lastUseTime = lastUseTime;
    }

    public Date getLastUseTime() 
    {
        return lastUseTime;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("sealId", getSealId())
            .append("sealName", getSealName())
            .append("sealType", getSealType())
            .append("sealNumber", getSealNumber())
            .append("sealImage", getSealImage())
            .append("managerId", getManagerId())
            .append("managerName", getManagerName())
            .append("status", getStatus())
            .append("enableDate", getEnableDate())
            .append("disableDate", getDisableDate())
            .append("useCount", getUseCount())
            .append("lastUseTime", getLastUseTime())
            .append("description", getDescription())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
