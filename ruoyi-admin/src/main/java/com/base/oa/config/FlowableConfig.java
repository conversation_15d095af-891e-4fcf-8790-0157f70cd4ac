package com.base.oa.config;

import org.flowable.common.engine.impl.history.HistoryLevel;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Flowable工作流引擎配置
 * 使用Spring Boot自动配置，避免冲突
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Configuration
public class FlowableConfig {

    /**
     * 配置Flowable引擎
     * 使用EngineConfigurationConfigurer来自定义配置，而不是创建新的ProcessEngine
     */
    @Bean
    public EngineConfigurationConfigurer<SpringProcessEngineConfiguration> processEngineConfigurer() {
        return engineConfiguration -> {
            // 设置字体，支持中文
            engineConfiguration.setActivityFontName("宋体");
            engineConfiguration.setLabelFontName("宋体");
            engineConfiguration.setAnnotationFontName("宋体");

            // 禁用异步执行器
            engineConfiguration.setAsyncExecutorActivate(false);

            // 设置数据库更新策略
            engineConfiguration.setDatabaseSchemaUpdate("true");

            // 设置历史级别
            engineConfiguration.setHistoryLevel(HistoryLevel.FULL);
        };
    }
}
