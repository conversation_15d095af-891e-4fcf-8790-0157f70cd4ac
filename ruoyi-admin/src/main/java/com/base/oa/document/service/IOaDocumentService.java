package com.base.oa.document.service;

import java.util.List;
import com.base.oa.document.domain.OaDocumentReceive;
import com.base.oa.document.domain.OaDocumentSend;

/**
 * 文档管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IOaDocumentService 
{
    // ==================== 收文管理 ====================
    
    /**
     * 查询收文管理
     * 
     * @param docId 收文管理主键
     * @return 收文管理
     */
    public OaDocumentReceive selectOaDocumentReceiveByDocId(Long docId);

    /**
     * 查询收文管理列表
     * 
     * @param oaDocumentReceive 收文管理
     * @return 收文管理集合
     */
    public List<OaDocumentReceive> selectOaDocumentReceiveList(OaDocumentReceive oaDocumentReceive);

    /**
     * 新增收文管理
     * 
     * @param oaDocumentReceive 收文管理
     * @return 结果
     */
    public int insertOaDocumentReceive(OaDocumentReceive oaDocumentReceive);

    /**
     * 修改收文管理
     * 
     * @param oaDocumentReceive 收文管理
     * @return 结果
     */
    public int updateOaDocumentReceive(OaDocumentReceive oaDocumentReceive);

    /**
     * 批量删除收文管理
     * 
     * @param docIds 需要删除的收文管理主键集合
     * @return 结果
     */
    public int deleteOaDocumentReceiveByDocIds(Long[] docIds);

    /**
     * 删除收文管理信息
     * 
     * @param docId 收文管理主键
     * @return 结果
     */
    public int deleteOaDocumentReceiveByDocId(Long docId);

    // ==================== 发文管理 ====================
    
    /**
     * 查询发文管理
     * 
     * @param docId 发文管理主键
     * @return 发文管理
     */
    public OaDocumentSend selectOaDocumentSendByDocId(Long docId);

    /**
     * 查询发文管理列表
     * 
     * @param oaDocumentSend 发文管理
     * @return 发文管理集合
     */
    public List<OaDocumentSend> selectOaDocumentSendList(OaDocumentSend oaDocumentSend);

    /**
     * 新增发文管理
     * 
     * @param oaDocumentSend 发文管理
     * @return 结果
     */
    public int insertOaDocumentSend(OaDocumentSend oaDocumentSend);

    /**
     * 修改发文管理
     * 
     * @param oaDocumentSend 发文管理
     * @return 结果
     */
    public int updateOaDocumentSend(OaDocumentSend oaDocumentSend);

    /**
     * 批量删除发文管理
     * 
     * @param docIds 需要删除的发文管理主键集合
     * @return 结果
     */
    public int deleteOaDocumentSendByDocIds(Long[] docIds);

    /**
     * 删除发文管理信息
     * 
     * @param docId 发文管理主键
     * @return 结果
     */
    public int deleteOaDocumentSendByDocId(Long docId);
}
