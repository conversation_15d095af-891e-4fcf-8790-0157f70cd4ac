package com.base.oa.document.mapper;

import java.util.List;
import com.base.oa.document.domain.OaDocumentReceive;

/**
 * 收文管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OaDocumentReceiveMapper 
{
    /**
     * 查询收文管理
     * 
     * @param docId 收文管理主键
     * @return 收文管理
     */
    public OaDocumentReceive selectOaDocumentReceiveByDocId(Long docId);

    /**
     * 查询收文管理列表
     * 
     * @param oaDocumentReceive 收文管理
     * @return 收文管理集合
     */
    public List<OaDocumentReceive> selectOaDocumentReceiveList(OaDocumentReceive oaDocumentReceive);

    /**
     * 新增收文管理
     * 
     * @param oaDocumentReceive 收文管理
     * @return 结果
     */
    public int insertOaDocumentReceive(OaDocumentReceive oaDocumentReceive);

    /**
     * 修改收文管理
     * 
     * @param oaDocumentReceive 收文管理
     * @return 结果
     */
    public int updateOaDocumentReceive(OaDocumentReceive oaDocumentReceive);

    /**
     * 删除收文管理
     * 
     * @param docId 收文管理主键
     * @return 结果
     */
    public int deleteOaDocumentReceiveByDocId(Long docId);

    /**
     * 批量删除收文管理
     * 
     * @param docIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaDocumentReceiveByDocIds(Long[] docIds);
}
