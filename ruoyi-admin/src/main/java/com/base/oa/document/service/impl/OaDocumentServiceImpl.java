package com.base.oa.document.service.impl;

import java.util.List;
import com.base.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.base.oa.document.mapper.OaDocumentReceiveMapper;
import com.base.oa.document.mapper.OaDocumentSendMapper;
import com.base.oa.document.domain.OaDocumentReceive;
import com.base.oa.document.domain.OaDocumentSend;
import com.base.oa.document.service.IOaDocumentService;

/**
 * 文档管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class OaDocumentServiceImpl implements IOaDocumentService 
{
    @Autowired
    private OaDocumentReceiveMapper oaDocumentReceiveMapper;
    
    @Autowired
    private OaDocumentSendMapper oaDocumentSendMapper;

    // ==================== 收文管理 ====================
    
    /**
     * 查询收文管理
     * 
     * @param docId 收文管理主键
     * @return 收文管理
     */
    @Override
    public OaDocumentReceive selectOaDocumentReceiveByDocId(Long docId)
    {
        return oaDocumentReceiveMapper.selectOaDocumentReceiveByDocId(docId);
    }

    /**
     * 查询收文管理列表
     * 
     * @param oaDocumentReceive 收文管理
     * @return 收文管理
     */
    @Override
    public List<OaDocumentReceive> selectOaDocumentReceiveList(OaDocumentReceive oaDocumentReceive)
    {
        return oaDocumentReceiveMapper.selectOaDocumentReceiveList(oaDocumentReceive);
    }

    /**
     * 新增收文管理
     * 
     * @param oaDocumentReceive 收文管理
     * @return 结果
     */
    @Override
    public int insertOaDocumentReceive(OaDocumentReceive oaDocumentReceive)
    {
        oaDocumentReceive.setCreateTime(DateUtils.getNowDate());
        return oaDocumentReceiveMapper.insertOaDocumentReceive(oaDocumentReceive);
    }

    /**
     * 修改收文管理
     * 
     * @param oaDocumentReceive 收文管理
     * @return 结果
     */
    @Override
    public int updateOaDocumentReceive(OaDocumentReceive oaDocumentReceive)
    {
        oaDocumentReceive.setUpdateTime(DateUtils.getNowDate());
        return oaDocumentReceiveMapper.updateOaDocumentReceive(oaDocumentReceive);
    }

    /**
     * 批量删除收文管理
     * 
     * @param docIds 需要删除的收文管理主键
     * @return 结果
     */
    @Override
    public int deleteOaDocumentReceiveByDocIds(Long[] docIds)
    {
        return oaDocumentReceiveMapper.deleteOaDocumentReceiveByDocIds(docIds);
    }

    /**
     * 删除收文管理信息
     * 
     * @param docId 收文管理主键
     * @return 结果
     */
    @Override
    public int deleteOaDocumentReceiveByDocId(Long docId)
    {
        return oaDocumentReceiveMapper.deleteOaDocumentReceiveByDocId(docId);
    }

    // ==================== 发文管理 ====================
    
    /**
     * 查询发文管理
     * 
     * @param docId 发文管理主键
     * @return 发文管理
     */
    @Override
    public OaDocumentSend selectOaDocumentSendByDocId(Long docId)
    {
        return oaDocumentSendMapper.selectOaDocumentSendByDocId(docId);
    }

    /**
     * 查询发文管理列表
     * 
     * @param oaDocumentSend 发文管理
     * @return 发文管理
     */
    @Override
    public List<OaDocumentSend> selectOaDocumentSendList(OaDocumentSend oaDocumentSend)
    {
        return oaDocumentSendMapper.selectOaDocumentSendList(oaDocumentSend);
    }

    /**
     * 新增发文管理
     * 
     * @param oaDocumentSend 发文管理
     * @return 结果
     */
    @Override
    public int insertOaDocumentSend(OaDocumentSend oaDocumentSend)
    {
        oaDocumentSend.setCreateTime(DateUtils.getNowDate());
        return oaDocumentSendMapper.insertOaDocumentSend(oaDocumentSend);
    }

    /**
     * 修改发文管理
     * 
     * @param oaDocumentSend 发文管理
     * @return 结果
     */
    @Override
    public int updateOaDocumentSend(OaDocumentSend oaDocumentSend)
    {
        oaDocumentSend.setUpdateTime(DateUtils.getNowDate());
        return oaDocumentSendMapper.updateOaDocumentSend(oaDocumentSend);
    }

    /**
     * 批量删除发文管理
     * 
     * @param docIds 需要删除的发文管理主键
     * @return 结果
     */
    @Override
    public int deleteOaDocumentSendByDocIds(Long[] docIds)
    {
        return oaDocumentSendMapper.deleteOaDocumentSendByDocIds(docIds);
    }

    /**
     * 删除发文管理信息
     * 
     * @param docId 发文管理主键
     * @return 结果
     */
    @Override
    public int deleteOaDocumentSendByDocId(Long docId)
    {
        return oaDocumentSendMapper.deleteOaDocumentSendByDocId(docId);
    }
}
