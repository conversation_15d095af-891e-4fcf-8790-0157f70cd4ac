package com.base.oa.document.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 公文管理对象 oa_document
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class OaDocument extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文档ID */
    private Long docId;

    /** 文档标题 */
    @Excel(name = "文档标题")
    private String docTitle;

    /** 文档编号 */
    @Excel(name = "文档编号")
    private String docNumber;

    /** 文档类型(1收文2发文) */
    @Excel(name = "文档类型", readConverterExp = "1=收文,2=发文")
    private String docType;

    /** 文档分类 */
    @Excel(name = "文档分类")
    private String docCategory;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private Long creatorId;

    /** 创建人姓名 */
    @Excel(name = "创建人姓名")
    private String creatorName;

    /** 所属部门ID */
    @Excel(name = "所属部门ID")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 紧急程度(1紧急2普通3缓办) */
    @Excel(name = "紧急程度", readConverterExp = "1=紧急,2=普通,3=缓办")
    private String urgencyLevel;

    /** 密级(1公开2内部3秘密4机密) */
    @Excel(name = "密级", readConverterExp = "1=公开,2=内部,3=秘密,4=机密")
    private String securityLevel;

    /** 状态(1草稿2审批中3已发布4已归档) */
    @Excel(name = "状态", readConverterExp = "1=草稿,2=审批中,3=已发布,4=已归档")
    private String status;

    /** 文档内容 */
    private String content;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /** 附件列表 */
    private List<OaFileAttachment> attachments;

    /** 流程实例ID */
    private Long instanceId;

    /** 当前任务 */
    private String currentTask;

    /** 来文单位 */
    @Excel(name = "来文单位")
    private String sourceUnit;

    /** 收文日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收文日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date receiveDate;

    /** 主送单位 */
    @Excel(name = "主送单位")
    private String mainSendUnit;

    /** 抄送单位 */
    @Excel(name = "抄送单位")
    private String copySendUnit;

    public void setDocId(Long docId) 
    {
        this.docId = docId;
    }

    public Long getDocId() 
    {
        return docId;
    }
    public void setDocTitle(String docTitle) 
    {
        this.docTitle = docTitle;
    }

    public String getDocTitle() 
    {
        return docTitle;
    }
    public void setDocNumber(String docNumber) 
    {
        this.docNumber = docNumber;
    }

    public String getDocNumber() 
    {
        return docNumber;
    }
    public void setDocType(String docType) 
    {
        this.docType = docType;
    }

    public String getDocType() 
    {
        return docType;
    }
    public void setDocCategory(String docCategory) 
    {
        this.docCategory = docCategory;
    }

    public String getDocCategory() 
    {
        return docCategory;
    }
    public void setCreatorId(Long creatorId) 
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() 
    {
        return creatorId;
    }
    public void setCreatorName(String creatorName) 
    {
        this.creatorName = creatorName;
    }

    public String getCreatorName() 
    {
        return creatorName;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }
    public void setUrgencyLevel(String urgencyLevel) 
    {
        this.urgencyLevel = urgencyLevel;
    }

    public String getUrgencyLevel() 
    {
        return urgencyLevel;
    }
    public void setSecurityLevel(String securityLevel) 
    {
        this.securityLevel = securityLevel;
    }

    public String getSecurityLevel() 
    {
        return securityLevel;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setPublishTime(Date publishTime) 
    {
        this.publishTime = publishTime;
    }

    public Date getPublishTime() 
    {
        return publishTime;
    }

    public List<OaFileAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<OaFileAttachment> attachments) {
        this.attachments = attachments;
    }

    public Long getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(Long instanceId) {
        this.instanceId = instanceId;
    }

    public String getCurrentTask() {
        return currentTask;
    }

    public void setCurrentTask(String currentTask) {
        this.currentTask = currentTask;
    }

    public String getSourceUnit() {
        return sourceUnit;
    }

    public void setSourceUnit(String sourceUnit) {
        this.sourceUnit = sourceUnit;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getMainSendUnit() {
        return mainSendUnit;
    }

    public void setMainSendUnit(String mainSendUnit) {
        this.mainSendUnit = mainSendUnit;
    }

    public String getCopySendUnit() {
        return copySendUnit;
    }

    public void setCopySendUnit(String copySendUnit) {
        this.copySendUnit = copySendUnit;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("docId", getDocId())
            .append("docTitle", getDocTitle())
            .append("docNumber", getDocNumber())
            .append("docType", getDocType())
            .append("docCategory", getDocCategory())
            .append("creatorId", getCreatorId())
            .append("creatorName", getCreatorName())
            .append("deptId", getDeptId())
            .append("urgencyLevel", getUrgencyLevel())
            .append("securityLevel", getSecurityLevel())
            .append("status", getStatus())
            .append("content", getContent())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("publishTime", getPublishTime())
            .toString();
    }
}
