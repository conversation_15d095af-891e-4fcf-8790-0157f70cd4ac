package com.base.oa.document.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.enums.BusinessType;
import com.base.oa.document.domain.OaDocumentReceive;
import com.base.oa.document.domain.OaDocumentSend;
import com.base.oa.document.service.IOaDocumentService;
import com.base.common.utils.poi.ExcelUtil;
import com.base.common.core.page.TableDataInfo;

/**
 * 文档管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/oa/document")
public class OaDocumentController extends BaseController
{
    @Autowired
    private IOaDocumentService oaDocumentService;

    // ==================== 收文管理 ====================

    /**
     * 查询收文管理列表
     */
    @PreAuthorize("@ss.hasPermi('oa:document:receive:list')")
    @GetMapping("/receive/list")
    public TableDataInfo receiveList(OaDocumentReceive oaDocumentReceive)
    {
        startPage();
        List<OaDocumentReceive> list = oaDocumentService.selectOaDocumentReceiveList(oaDocumentReceive);
        return getDataTable(list);
    }

    /**
     * 导出收文管理列表
     */
    @PreAuthorize("@ss.hasPermi('oa:document:receive:export')")
    @Log(title = "收文管理", businessType = BusinessType.EXPORT)
    @PostMapping("/receive/export")
    public void receiveExport(HttpServletResponse response, OaDocumentReceive oaDocumentReceive)
    {
        List<OaDocumentReceive> list = oaDocumentService.selectOaDocumentReceiveList(oaDocumentReceive);
        ExcelUtil<OaDocumentReceive> util = new ExcelUtil<OaDocumentReceive>(OaDocumentReceive.class);
        util.exportExcel(response, list, "收文管理数据");
    }

    /**
     * 获取收文管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:document:receive:query')")
    @GetMapping("/receive/{docId}")
    public AjaxResult getReceiveInfo(@PathVariable("docId") Long docId)
    {
        return success(oaDocumentService.selectOaDocumentReceiveByDocId(docId));
    }

    /**
     * 新增收文管理
     */
    @PreAuthorize("@ss.hasPermi('oa:document:receive:add')")
    @Log(title = "收文管理", businessType = BusinessType.INSERT)
    @PostMapping("/receive")
    public AjaxResult addReceive(@RequestBody OaDocumentReceive oaDocumentReceive)
    {
        return toAjax(oaDocumentService.insertOaDocumentReceive(oaDocumentReceive));
    }

    /**
     * 修改收文管理
     */
    @PreAuthorize("@ss.hasPermi('oa:document:receive:edit')")
    @Log(title = "收文管理", businessType = BusinessType.UPDATE)
    @PutMapping("/receive")
    public AjaxResult editReceive(@RequestBody OaDocumentReceive oaDocumentReceive)
    {
        return toAjax(oaDocumentService.updateOaDocumentReceive(oaDocumentReceive));
    }

    /**
     * 删除收文管理
     */
    @PreAuthorize("@ss.hasPermi('oa:document:receive:remove')")
    @Log(title = "收文管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/receive/{docIds}")
    public AjaxResult removeReceive(@PathVariable Long[] docIds)
    {
        return toAjax(oaDocumentService.deleteOaDocumentReceiveByDocIds(docIds));
    }

    // ==================== 发文管理 ====================

    /**
     * 查询发文管理列表
     */
    @PreAuthorize("@ss.hasPermi('oa:document:send:list')")
    @GetMapping("/send/list")
    public TableDataInfo sendList(OaDocumentSend oaDocumentSend)
    {
        startPage();
        List<OaDocumentSend> list = oaDocumentService.selectOaDocumentSendList(oaDocumentSend);
        return getDataTable(list);
    }

    /**
     * 导出发文管理列表
     */
    @PreAuthorize("@ss.hasPermi('oa:document:send:export')")
    @Log(title = "发文管理", businessType = BusinessType.EXPORT)
    @PostMapping("/send/export")
    public void sendExport(HttpServletResponse response, OaDocumentSend oaDocumentSend)
    {
        List<OaDocumentSend> list = oaDocumentService.selectOaDocumentSendList(oaDocumentSend);
        ExcelUtil<OaDocumentSend> util = new ExcelUtil<OaDocumentSend>(OaDocumentSend.class);
        util.exportExcel(response, list, "发文管理数据");
    }

    /**
     * 获取发文管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:document:send:query')")
    @GetMapping("/send/{docId}")
    public AjaxResult getSendInfo(@PathVariable("docId") Long docId)
    {
        return success(oaDocumentService.selectOaDocumentSendByDocId(docId));
    }

    /**
     * 新增发文管理
     */
    @PreAuthorize("@ss.hasPermi('oa:document:send:add')")
    @Log(title = "发文管理", businessType = BusinessType.INSERT)
    @PostMapping("/send")
    public AjaxResult addSend(@RequestBody OaDocumentSend oaDocumentSend)
    {
        return toAjax(oaDocumentService.insertOaDocumentSend(oaDocumentSend));
    }

    /**
     * 修改发文管理
     */
    @PreAuthorize("@ss.hasPermi('oa:document:send:edit')")
    @Log(title = "发文管理", businessType = BusinessType.UPDATE)
    @PutMapping("/send")
    public AjaxResult editSend(@RequestBody OaDocumentSend oaDocumentSend)
    {
        return toAjax(oaDocumentService.updateOaDocumentSend(oaDocumentSend));
    }

    /**
     * 删除发文管理
     */
    @PreAuthorize("@ss.hasPermi('oa:document:send:remove')")
    @Log(title = "发文管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/send/{docIds}")
    public AjaxResult removeSend(@PathVariable Long[] docIds)
    {
        return toAjax(oaDocumentService.deleteOaDocumentSendByDocIds(docIds));
    }
}
