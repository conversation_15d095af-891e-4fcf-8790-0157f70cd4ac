package com.base.oa.document.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;

/**
 * 发文管理对象 oa_document_send
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class OaDocumentSend extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文档ID */
    private Long docId;

    /** 文档编号 */
    @Excel(name = "文档编号")
    private String docNumber;

    /** 文档标题 */
    @Excel(name = "文档标题")
    private String docTitle;

    /** 文档类型 */
    @Excel(name = "文档类型")
    private String docType;

    /** 收文单位 */
    @Excel(name = "收文单位")
    private String receiverUnit;

    /** 收文联系人 */
    @Excel(name = "收文联系人")
    private String receiverContact;

    /** 发文日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发文日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sendDate;

    /** 紧急程度 */
    @Excel(name = "紧急程度")
    private String urgencyLevel;

    /** 密级 */
    @Excel(name = "密级")
    private String securityLevel;

    /** 文档内容 */
    private String docContent;

    /** 附件 */
    private String attachments;

    /** 起草人ID */
    private Long drafterId;

    /** 起草人姓名 */
    @Excel(name = "起草人姓名")
    private String drafterName;

    /** 起草时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "起草时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date draftTime;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 工作流实例ID */
    private String workflowInstanceId;

    /** 当前步骤 */
    @Excel(name = "当前步骤")
    private String currentStep;

    /** 当前处理人 */
    @Excel(name = "当前处理人")
    private String currentAssignee;

    public void setDocId(Long docId) 
    {
        this.docId = docId;
    }

    public Long getDocId() 
    {
        return docId;
    }
    public void setDocNumber(String docNumber) 
    {
        this.docNumber = docNumber;
    }

    public String getDocNumber() 
    {
        return docNumber;
    }
    public void setDocTitle(String docTitle) 
    {
        this.docTitle = docTitle;
    }

    public String getDocTitle() 
    {
        return docTitle;
    }
    public void setDocType(String docType) 
    {
        this.docType = docType;
    }

    public String getDocType() 
    {
        return docType;
    }
    public void setReceiverUnit(String receiverUnit) 
    {
        this.receiverUnit = receiverUnit;
    }

    public String getReceiverUnit() 
    {
        return receiverUnit;
    }
    public void setReceiverContact(String receiverContact) 
    {
        this.receiverContact = receiverContact;
    }

    public String getReceiverContact() 
    {
        return receiverContact;
    }
    public void setSendDate(Date sendDate) 
    {
        this.sendDate = sendDate;
    }

    public Date getSendDate() 
    {
        return sendDate;
    }
    public void setUrgencyLevel(String urgencyLevel) 
    {
        this.urgencyLevel = urgencyLevel;
    }

    public String getUrgencyLevel() 
    {
        return urgencyLevel;
    }
    public void setSecurityLevel(String securityLevel) 
    {
        this.securityLevel = securityLevel;
    }

    public String getSecurityLevel() 
    {
        return securityLevel;
    }
    public void setDocContent(String docContent) 
    {
        this.docContent = docContent;
    }

    public String getDocContent() 
    {
        return docContent;
    }
    public void setAttachments(String attachments) 
    {
        this.attachments = attachments;
    }

    public String getAttachments() 
    {
        return attachments;
    }
    public void setDrafterId(Long drafterId) 
    {
        this.drafterId = drafterId;
    }

    public Long getDrafterId() 
    {
        return drafterId;
    }
    public void setDrafterName(String drafterName) 
    {
        this.drafterName = drafterName;
    }

    public String getDrafterName() 
    {
        return drafterName;
    }
    public void setDraftTime(Date draftTime) 
    {
        this.draftTime = draftTime;
    }

    public Date getDraftTime() 
    {
        return draftTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setWorkflowInstanceId(String workflowInstanceId) 
    {
        this.workflowInstanceId = workflowInstanceId;
    }

    public String getWorkflowInstanceId() 
    {
        return workflowInstanceId;
    }
    public void setCurrentStep(String currentStep) 
    {
        this.currentStep = currentStep;
    }

    public String getCurrentStep() 
    {
        return currentStep;
    }
    public void setCurrentAssignee(String currentAssignee) 
    {
        this.currentAssignee = currentAssignee;
    }

    public String getCurrentAssignee() 
    {
        return currentAssignee;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("docId", getDocId())
            .append("docNumber", getDocNumber())
            .append("docTitle", getDocTitle())
            .append("docType", getDocType())
            .append("receiverUnit", getReceiverUnit())
            .append("receiverContact", getReceiverContact())
            .append("sendDate", getSendDate())
            .append("urgencyLevel", getUrgencyLevel())
            .append("securityLevel", getSecurityLevel())
            .append("docContent", getDocContent())
            .append("attachments", getAttachments())
            .append("drafterId", getDrafterId())
            .append("drafterName", getDrafterName())
            .append("draftTime", getDraftTime())
            .append("status", getStatus())
            .append("workflowInstanceId", getWorkflowInstanceId())
            .append("currentStep", getCurrentStep())
            .append("currentAssignee", getCurrentAssignee())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
