package com.base.oa.document.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;

/**
 * 收文管理对象 oa_document_receive
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class OaDocumentReceive extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文档ID */
    private Long docId;

    /** 文档编号 */
    @Excel(name = "文档编号")
    private String docNumber;

    /** 文档标题 */
    @Excel(name = "文档标题")
    private String docTitle;

    /** 文档类型 */
    @Excel(name = "文档类型")
    private String docType;

    /** 发文单位 */
    @Excel(name = "发文单位")
    private String senderUnit;

    /** 发文联系人 */
    @Excel(name = "发文联系人")
    private String senderContact;

    /** 收文日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "收文日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date receiveDate;

    /** 紧急程度 */
    @Excel(name = "紧急程度")
    private String urgencyLevel;

    /** 密级 */
    @Excel(name = "密级")
    private String securityLevel;

    /** 文档内容 */
    private String docContent;

    /** 附件 */
    private String attachments;

    /** 登记人ID */
    private Long registrarId;

    /** 登记人姓名 */
    @Excel(name = "登记人姓名")
    private String registrarName;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登记时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 工作流实例ID */
    private String workflowInstanceId;

    /** 当前步骤 */
    @Excel(name = "当前步骤")
    private String currentStep;

    /** 当前处理人 */
    @Excel(name = "当前处理人")
    private String currentAssignee;

    public void setDocId(Long docId) 
    {
        this.docId = docId;
    }

    public Long getDocId() 
    {
        return docId;
    }
    public void setDocNumber(String docNumber) 
    {
        this.docNumber = docNumber;
    }

    public String getDocNumber() 
    {
        return docNumber;
    }
    public void setDocTitle(String docTitle) 
    {
        this.docTitle = docTitle;
    }

    public String getDocTitle() 
    {
        return docTitle;
    }
    public void setDocType(String docType) 
    {
        this.docType = docType;
    }

    public String getDocType() 
    {
        return docType;
    }
    public void setSenderUnit(String senderUnit) 
    {
        this.senderUnit = senderUnit;
    }

    public String getSenderUnit() 
    {
        return senderUnit;
    }
    public void setSenderContact(String senderContact) 
    {
        this.senderContact = senderContact;
    }

    public String getSenderContact() 
    {
        return senderContact;
    }
    public void setReceiveDate(Date receiveDate) 
    {
        this.receiveDate = receiveDate;
    }

    public Date getReceiveDate() 
    {
        return receiveDate;
    }
    public void setUrgencyLevel(String urgencyLevel) 
    {
        this.urgencyLevel = urgencyLevel;
    }

    public String getUrgencyLevel() 
    {
        return urgencyLevel;
    }
    public void setSecurityLevel(String securityLevel) 
    {
        this.securityLevel = securityLevel;
    }

    public String getSecurityLevel() 
    {
        return securityLevel;
    }
    public void setDocContent(String docContent) 
    {
        this.docContent = docContent;
    }

    public String getDocContent() 
    {
        return docContent;
    }
    public void setAttachments(String attachments) 
    {
        this.attachments = attachments;
    }

    public String getAttachments() 
    {
        return attachments;
    }
    public void setRegistrarId(Long registrarId) 
    {
        this.registrarId = registrarId;
    }

    public Long getRegistrarId() 
    {
        return registrarId;
    }
    public void setRegistrarName(String registrarName) 
    {
        this.registrarName = registrarName;
    }

    public String getRegistrarName() 
    {
        return registrarName;
    }
    public void setRegisterTime(Date registerTime) 
    {
        this.registerTime = registerTime;
    }

    public Date getRegisterTime() 
    {
        return registerTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setWorkflowInstanceId(String workflowInstanceId) 
    {
        this.workflowInstanceId = workflowInstanceId;
    }

    public String getWorkflowInstanceId() 
    {
        return workflowInstanceId;
    }
    public void setCurrentStep(String currentStep) 
    {
        this.currentStep = currentStep;
    }

    public String getCurrentStep() 
    {
        return currentStep;
    }
    public void setCurrentAssignee(String currentAssignee) 
    {
        this.currentAssignee = currentAssignee;
    }

    public String getCurrentAssignee() 
    {
        return currentAssignee;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("docId", getDocId())
            .append("docNumber", getDocNumber())
            .append("docTitle", getDocTitle())
            .append("docType", getDocType())
            .append("senderUnit", getSenderUnit())
            .append("senderContact", getSenderContact())
            .append("receiveDate", getReceiveDate())
            .append("urgencyLevel", getUrgencyLevel())
            .append("securityLevel", getSecurityLevel())
            .append("docContent", getDocContent())
            .append("attachments", getAttachments())
            .append("registrarId", getRegistrarId())
            .append("registrarName", getRegistrarName())
            .append("registerTime", getRegisterTime())
            .append("status", getStatus())
            .append("workflowInstanceId", getWorkflowInstanceId())
            .append("currentStep", getCurrentStep())
            .append("currentAssignee", getCurrentAssignee())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
