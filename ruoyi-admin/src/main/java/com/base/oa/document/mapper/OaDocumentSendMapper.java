package com.base.oa.document.mapper;

import java.util.List;
import com.base.oa.document.domain.OaDocumentSend;

/**
 * 发文管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OaDocumentSendMapper 
{
    /**
     * 查询发文管理
     * 
     * @param docId 发文管理主键
     * @return 发文管理
     */
    public OaDocumentSend selectOaDocumentSendByDocId(Long docId);

    /**
     * 查询发文管理列表
     * 
     * @param oaDocumentSend 发文管理
     * @return 发文管理集合
     */
    public List<OaDocumentSend> selectOaDocumentSendList(OaDocumentSend oaDocumentSend);

    /**
     * 新增发文管理
     * 
     * @param oaDocumentSend 发文管理
     * @return 结果
     */
    public int insertOaDocumentSend(OaDocumentSend oaDocumentSend);

    /**
     * 修改发文管理
     * 
     * @param oaDocumentSend 发文管理
     * @return 结果
     */
    public int updateOaDocumentSend(OaDocumentSend oaDocumentSend);

    /**
     * 删除发文管理
     * 
     * @param docId 发文管理主键
     * @return 结果
     */
    public int deleteOaDocumentSendByDocId(Long docId);

    /**
     * 批量删除发文管理
     * 
     * @param docIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaDocumentSendByDocIds(Long[] docIds);
}
