package com.base.oa.meeting.service;

import java.util.List;
import com.base.oa.meeting.domain.OaMeeting;

/**
 * 会议管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IOaMeetingService 
{
    /**
     * 查询会议管理
     * 
     * @param meetingId 会议管理主键
     * @return 会议管理
     */
    public OaMeeting selectOaMeetingByMeetingId(Long meetingId);

    /**
     * 查询会议管理列表
     * 
     * @param oaMeeting 会议管理
     * @return 会议管理集合
     */
    public List<OaMeeting> selectOaMeetingList(OaMeeting oaMeeting);

    /**
     * 新增会议管理
     * 
     * @param oaMeeting 会议管理
     * @return 结果
     */
    public int insertOaMeeting(OaMeeting oaMeeting);

    /**
     * 修改会议管理
     * 
     * @param oaMeeting 会议管理
     * @return 结果
     */
    public int updateOaMeeting(OaMeeting oaMeeting);

    /**
     * 批量删除会议管理
     * 
     * @param meetingIds 需要删除的会议管理主键集合
     * @return 结果
     */
    public int deleteOaMeetingByMeetingIds(Long[] meetingIds);

    /**
     * 删除会议管理信息
     * 
     * @param meetingId 会议管理主键
     * @return 结果
     */
    public int deleteOaMeetingByMeetingId(Long meetingId);

    /**
     * 查询我的会议列表
     * 
     * @param userId 用户ID
     * @param oaMeeting 查询条件
     * @return 会议列表
     */
    public List<OaMeeting> selectMyMeetingList(Long userId, OaMeeting oaMeeting);

    /**
     * 会议签到
     * 
     * @param meetingId 会议ID
     * @param userId 用户ID
     * @return 结果
     */
    public int signInMeeting(Long meetingId, Long userId);

    /**
     * 取消会议
     * 
     * @param meetingId 会议ID
     * @param cancelReason 取消原因
     * @return 结果
     */
    public int cancelMeeting(Long meetingId, String cancelReason);

    /**
     * 结束会议
     * 
     * @param meetingId 会议ID
     * @param meetingSummary 会议纪要
     * @return 结果
     */
    public int endMeeting(Long meetingId, String meetingSummary);
}
