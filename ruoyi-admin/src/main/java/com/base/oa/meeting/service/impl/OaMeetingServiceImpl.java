package com.base.oa.meeting.service.impl;

import java.util.List;
import com.base.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.base.oa.meeting.mapper.OaMeetingMapper;
import com.base.oa.meeting.domain.OaMeeting;
import com.base.oa.meeting.service.IOaMeetingService;

/**
 * 会议管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class OaMeetingServiceImpl implements IOaMeetingService 
{
    @Autowired
    private OaMeetingMapper oaMeetingMapper;

    /**
     * 查询会议管理
     * 
     * @param meetingId 会议管理主键
     * @return 会议管理
     */
    @Override
    public OaMeeting selectOaMeetingByMeetingId(Long meetingId)
    {
        return oaMeetingMapper.selectOaMeetingByMeetingId(meetingId);
    }

    /**
     * 查询会议管理列表
     * 
     * @param oaMeeting 会议管理
     * @return 会议管理
     */
    @Override
    public List<OaMeeting> selectOaMeetingList(OaMeeting oaMeeting)
    {
        return oaMeetingMapper.selectOaMeetingList(oaMeeting);
    }

    /**
     * 新增会议管理
     * 
     * @param oaMeeting 会议管理
     * @return 结果
     */
    @Override
    public int insertOaMeeting(OaMeeting oaMeeting)
    {
        oaMeeting.setCreateTime(DateUtils.getNowDate());
        oaMeeting.setStatus("0"); // 待开始
        return oaMeetingMapper.insertOaMeeting(oaMeeting);
    }

    /**
     * 修改会议管理
     * 
     * @param oaMeeting 会议管理
     * @return 结果
     */
    @Override
    public int updateOaMeeting(OaMeeting oaMeeting)
    {
        oaMeeting.setUpdateTime(DateUtils.getNowDate());
        return oaMeetingMapper.updateOaMeeting(oaMeeting);
    }

    /**
     * 批量删除会议管理
     * 
     * @param meetingIds 需要删除的会议管理主键
     * @return 结果
     */
    @Override
    public int deleteOaMeetingByMeetingIds(Long[] meetingIds)
    {
        return oaMeetingMapper.deleteOaMeetingByMeetingIds(meetingIds);
    }

    /**
     * 删除会议管理信息
     * 
     * @param meetingId 会议管理主键
     * @return 结果
     */
    @Override
    public int deleteOaMeetingByMeetingId(Long meetingId)
    {
        return oaMeetingMapper.deleteOaMeetingByMeetingId(meetingId);
    }

    /**
     * 查询我的会议列表
     */
    @Override
    public List<OaMeeting> selectMyMeetingList(Long userId, OaMeeting oaMeeting)
    {
        // 查询用户参与的会议（主持人或参与者）
        return oaMeetingMapper.selectOaMeetingList(oaMeeting);
    }

    /**
     * 会议签到
     */
    @Override
    public int signInMeeting(Long meetingId, Long userId)
    {
        // 这里可以实现会议签到逻辑
        // 更新会议参与者签到状态
        return 1;
    }

    /**
     * 取消会议
     */
    @Override
    public int cancelMeeting(Long meetingId, String cancelReason)
    {
        OaMeeting meeting = new OaMeeting();
        meeting.setMeetingId(meetingId);
        meeting.setStatus("3"); // 已取消
        meeting.setRemark(cancelReason);
        meeting.setUpdateTime(DateUtils.getNowDate());
        return oaMeetingMapper.updateOaMeeting(meeting);
    }

    /**
     * 结束会议
     */
    @Override
    public int endMeeting(Long meetingId, String meetingSummary)
    {
        OaMeeting meeting = new OaMeeting();
        meeting.setMeetingId(meetingId);
        meeting.setStatus("2"); // 已结束
        meeting.setMinutes(meetingSummary); // 使用 minutes 字段存储会议纪要
        meeting.setUpdateTime(DateUtils.getNowDate());
        return oaMeetingMapper.updateOaMeeting(meeting);
    }
}
