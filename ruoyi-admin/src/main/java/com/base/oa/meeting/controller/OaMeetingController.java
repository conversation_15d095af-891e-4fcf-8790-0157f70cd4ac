package com.base.oa.meeting.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.enums.BusinessType;
import com.base.common.utils.SecurityUtils;
import com.base.oa.meeting.domain.OaMeeting;
import com.base.oa.meeting.service.IOaMeetingService;
import com.base.common.utils.poi.ExcelUtil;
import com.base.common.core.page.TableDataInfo;

/**
 * 会议管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/oa/meeting")
public class OaMeetingController extends BaseController
{
    @Autowired
    private IOaMeetingService oaMeetingService;

    /**
     * 查询会议管理列表
     */
    @PreAuthorize("@ss.hasPermi('oa:meeting:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaMeeting oaMeeting)
    {
        startPage();
        List<OaMeeting> list = oaMeetingService.selectOaMeetingList(oaMeeting);
        return getDataTable(list);
    }

    /**
     * 查询我的会议列表
     */
    @GetMapping("/my-meeting/list")
    public TableDataInfo myMeetingList(OaMeeting oaMeeting)
    {
        startPage();
        Long userId = SecurityUtils.getUserId();
        List<OaMeeting> list = oaMeetingService.selectMyMeetingList(userId, oaMeeting);
        return getDataTable(list);
    }

    /**
     * 导出会议管理列表
     */
    @PreAuthorize("@ss.hasPermi('oa:meeting:export')")
    @Log(title = "会议管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaMeeting oaMeeting)
    {
        List<OaMeeting> list = oaMeetingService.selectOaMeetingList(oaMeeting);
        ExcelUtil<OaMeeting> util = new ExcelUtil<OaMeeting>(OaMeeting.class);
        util.exportExcel(response, list, "会议管理数据");
    }

    /**
     * 获取会议管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:meeting:query')")
    @GetMapping(value = "/{meetingId}")
    public AjaxResult getInfo(@PathVariable("meetingId") Long meetingId)
    {
        return success(oaMeetingService.selectOaMeetingByMeetingId(meetingId));
    }

    /**
     * 新增会议管理
     */
    @PreAuthorize("@ss.hasPermi('oa:meeting:add')")
    @Log(title = "会议管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaMeeting oaMeeting)
    {
        return toAjax(oaMeetingService.insertOaMeeting(oaMeeting));
    }

    /**
     * 修改会议管理
     */
    @PreAuthorize("@ss.hasPermi('oa:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaMeeting oaMeeting)
    {
        return toAjax(oaMeetingService.updateOaMeeting(oaMeeting));
    }

    /**
     * 删除会议管理
     */
    @PreAuthorize("@ss.hasPermi('oa:meeting:remove')")
    @Log(title = "会议管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{meetingIds}")
    public AjaxResult remove(@PathVariable Long[] meetingIds)
    {
        return toAjax(oaMeetingService.deleteOaMeetingByMeetingIds(meetingIds));
    }

    /**
     * 会议签到
     */
    @PostMapping("/sign-in/{meetingId}")
    public AjaxResult signIn(@PathVariable Long meetingId)
    {
        Long userId = SecurityUtils.getUserId();
        return toAjax(oaMeetingService.signInMeeting(meetingId, userId));
    }

    /**
     * 取消会议
     */
    @PostMapping("/cancel/{meetingId}")
    public AjaxResult cancel(@PathVariable Long meetingId, @RequestParam String cancelReason)
    {
        return toAjax(oaMeetingService.cancelMeeting(meetingId, cancelReason));
    }

    /**
     * 结束会议
     */
    @PostMapping("/end/{meetingId}")
    public AjaxResult end(@PathVariable Long meetingId, @RequestParam String meetingSummary)
    {
        return toAjax(oaMeetingService.endMeeting(meetingId, meetingSummary));
    }
}
