package com.base.oa.meeting.mapper;

import java.util.List;
import com.base.oa.meeting.domain.OaMeeting;

/**
 * 会议管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OaMeetingMapper 
{
    /**
     * 查询会议管理
     * 
     * @param meetingId 会议管理主键
     * @return 会议管理
     */
    public OaMeeting selectOaMeetingByMeetingId(Long meetingId);

    /**
     * 查询会议管理列表
     * 
     * @param oaMeeting 会议管理
     * @return 会议管理集合
     */
    public List<OaMeeting> selectOaMeetingList(OaMeeting oaMeeting);

    /**
     * 新增会议管理
     * 
     * @param oaMeeting 会议管理
     * @return 结果
     */
    public int insertOaMeeting(OaMeeting oaMeeting);

    /**
     * 修改会议管理
     * 
     * @param oaMeeting 会议管理
     * @return 结果
     */
    public int updateOaMeeting(OaMeeting oaMeeting);

    /**
     * 删除会议管理
     * 
     * @param meetingId 会议管理主键
     * @return 结果
     */
    public int deleteOaMeetingByMeetingId(Long meetingId);

    /**
     * 批量删除会议管理
     * 
     * @param meetingIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaMeetingByMeetingIds(Long[] meetingIds);
}
