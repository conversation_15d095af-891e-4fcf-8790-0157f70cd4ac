package com.base.oa.workflow.mapper;

import java.util.List;
import com.base.oa.workflow.domain.OaWorkflowTask;

/**
 * 工作流任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OaWorkflowTaskMapper 
{
    /**
     * 查询工作流任务
     * 
     * @param taskId 工作流任务主键
     * @return 工作流任务
     */
    public OaWorkflowTask selectOaWorkflowTaskByTaskId(String taskId);

    /**
     * 查询工作流任务列表
     * 
     * @param oaWorkflowTask 工作流任务
     * @return 工作流任务集合
     */
    public List<OaWorkflowTask> selectOaWorkflowTaskList(OaWorkflowTask oaWorkflowTask);

    /**
     * 查询用户待办任务列表
     * 
     * @param assigneeId 处理人ID
     * @return 工作流任务集合
     */
    public List<OaWorkflowTask> selectTodoTasksByAssigneeId(Long assigneeId);

    /**
     * 查询用户已办任务列表
     * 
     * @param assigneeId 处理人ID
     * @return 工作流任务集合
     */
    public List<OaWorkflowTask> selectDoneTasksByAssigneeId(Long assigneeId);

    /**
     * 查询流程实例的任务列表
     * 
     * @param instanceId 流程实例ID
     * @return 工作流任务集合
     */
    public List<OaWorkflowTask> selectOaWorkflowTaskByInstanceId(Long instanceId);

    /**
     * 新增工作流任务
     * 
     * @param oaWorkflowTask 工作流任务
     * @return 结果
     */
    public int insertOaWorkflowTask(OaWorkflowTask oaWorkflowTask);

    /**
     * 修改工作流任务
     * 
     * @param oaWorkflowTask 工作流任务
     * @return 结果
     */
    public int updateOaWorkflowTask(OaWorkflowTask oaWorkflowTask);

    /**
     * 删除工作流任务
     * 
     * @param taskId 工作流任务主键
     * @return 结果
     */
    public int deleteOaWorkflowTaskByTaskId(String taskId);

    /**
     * 批量删除工作流任务
     * 
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaWorkflowTaskByTaskIds(String[] taskIds);
}
