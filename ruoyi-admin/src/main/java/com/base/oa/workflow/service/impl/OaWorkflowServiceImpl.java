package com.base.oa.workflow.service.impl;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Date;
import java.io.InputStream;

import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.oa.workflow.domain.OaWorkflowDefinition;
import com.base.oa.workflow.domain.OaWorkflowInstance;
import com.base.oa.workflow.domain.OaWorkflowTask;
import com.base.oa.workflow.mapper.OaWorkflowDefinitionMapper;
import com.base.oa.workflow.mapper.OaWorkflowInstanceMapper;
import com.base.oa.workflow.mapper.OaWorkflowTaskMapper;
import com.base.oa.workflow.service.IOaWorkflowService;

/**
 * 工作流服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class OaWorkflowServiceImpl implements IOaWorkflowService 
{
    @Autowired
    private OaWorkflowDefinitionMapper workflowDefinitionMapper;

    @Autowired
    private OaWorkflowInstanceMapper workflowInstanceMapper;

    @Autowired
    private OaWorkflowTaskMapper workflowTaskMapper;

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private HistoryService historyService;

    /**
     * 查询工作流定义列表
     *
     * @param oaWorkflowDefinition 工作流定义
     * @return 工作流定义
     */
    @Override
    public List<OaWorkflowDefinition> selectOaWorkflowDefinitionList(OaWorkflowDefinition oaWorkflowDefinition)
    {
        return workflowDefinitionMapper.selectOaWorkflowDefinitionList(oaWorkflowDefinition);
    }

    /**
     * 查询工作流定义
     *
     * @param workflowId 工作流定义主键
     * @return 工作流定义
     */
    @Override
    public OaWorkflowDefinition selectOaWorkflowDefinitionByWorkflowId(Long workflowId)
    {
        return workflowDefinitionMapper.selectOaWorkflowDefinitionByWorkflowId(workflowId);
    }

    /**
     * 新增工作流定义
     * 
     * @param oaWorkflowDefinition 工作流定义
     * @return 结果
     */
    @Override
    @Transactional
    public int insertOaWorkflowDefinition(OaWorkflowDefinition oaWorkflowDefinition)
    {
        oaWorkflowDefinition.setCreateTime(DateUtils.getNowDate());
        oaWorkflowDefinition.setCreateBy(SecurityUtils.getUsername());
        
        // 部署流程到Flowable引擎
        if (oaWorkflowDefinition.getWorkflowXml() != null) {
            String deploymentId = deployProcess(
                oaWorkflowDefinition.getWorkflowName(),
                oaWorkflowDefinition.getWorkflowKey(),
                oaWorkflowDefinition.getWorkflowXml()
            );
            // 可以将deploymentId保存到数据库中
        }
        
        return workflowDefinitionMapper.insertOaWorkflowDefinition(oaWorkflowDefinition);
    }

    /**
     * 修改工作流定义
     * 
     * @param oaWorkflowDefinition 工作流定义
     * @return 结果
     */
    @Override
    public int updateOaWorkflowDefinition(OaWorkflowDefinition oaWorkflowDefinition)
    {
        oaWorkflowDefinition.setUpdateTime(DateUtils.getNowDate());
        oaWorkflowDefinition.setUpdateBy(SecurityUtils.getUsername());
        return workflowDefinitionMapper.updateOaWorkflowDefinition(oaWorkflowDefinition);
    }

    /**
     * 删除工作流定义
     *
     * @param workflowId 工作流定义主键
     * @return 结果
     */
    @Override
    public int deleteOaWorkflowDefinitionByWorkflowId(Long workflowId)
    {
        return workflowDefinitionMapper.deleteOaWorkflowDefinitionByWorkflowId(workflowId);
    }

    /**
     * 批量删除工作流定义
     *
     * @param workflowIds 工作流定义主键数组
     * @return 结果
     */
    @Override
    public int deleteOaWorkflowDefinitionByWorkflowIds(Long[] workflowIds)
    {
        return workflowDefinitionMapper.deleteOaWorkflowDefinitionByWorkflowIds(workflowIds);
    }

    /**
     * 启动工作流程
     * 
     * @param workflowKey 流程标识
     * @param businessKey 业务主键
     * @param variables 流程变量
     * @return 流程实例ID
     */
    @Override
    @Transactional
    public String startProcess(String workflowKey, String businessKey, Map<String, Object> variables)
    {
        // 启动流程实例
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(workflowKey, businessKey, variables);
        
        // 保存流程实例信息到数据库
        OaWorkflowInstance instance = new OaWorkflowInstance();
        instance.setBusinessKey(businessKey);
        instance.setStartUserId(SecurityUtils.getUserId());
        instance.setStartUserName(SecurityUtils.getUsername());
        instance.setStatus("1"); // 进行中
        instance.setStartTime(new Date());
        instance.setCreateTime(new Date());
        
        // 查询流程定义信息
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
            .processDefinitionId(processInstance.getProcessDefinitionId())
            .singleResult();
        
        if (processDefinition != null) {
            OaWorkflowDefinition definition = workflowDefinitionMapper.selectOaWorkflowDefinitionByKey(workflowKey);
            if (definition != null) {
                instance.setWorkflowId(definition.getWorkflowId());
            }
        }
        
        workflowInstanceMapper.insertOaWorkflowInstance(instance);
        
        return processInstance.getId();
    }

    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @param variables 流程变量
     * @param comment 处理意见
     * @return 结果
     */
    @Override
    @Transactional
    public boolean completeTask(String taskId, Map<String, Object> variables, String comment)
    {
        try {
            // 添加任务评论
            if (comment != null && !comment.isEmpty()) {
                taskService.addComment(taskId, null, comment);
            }
            
            // 完成任务
            taskService.complete(taskId, variables);
            
            // 更新任务状态
            OaWorkflowTask task = workflowTaskMapper.selectOaWorkflowTaskByTaskId(taskId);
            if (task != null) {
                task.setStatus("2"); // 已处理
                task.setCompleteTime(new Date());
                task.setComment(comment);
                workflowTaskMapper.updateOaWorkflowTask(task);
            }
            
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 查询待办任务列表
     * 
     * @param userId 用户ID
     * @return 待办任务集合
     */
    @Override
    public List<OaWorkflowTask> selectTodoTasks(Long userId)
    {
        // 从Flowable查询待办任务
        List<Task> tasks = taskService.createTaskQuery()
            .taskAssignee(userId.toString())
            .active()
            .orderByTaskCreateTime()
            .desc()
            .list();
        
        List<OaWorkflowTask> todoTasks = new ArrayList<>();
        for (Task task : tasks) {
            OaWorkflowTask oaTask = convertToOaWorkflowTask(task);
            todoTasks.add(oaTask);
        }
        
        return todoTasks;
    }

    /**
     * 查询已办任务列表
     * 
     * @param userId 用户ID
     * @return 已办任务集合
     */
    @Override
    public List<OaWorkflowTask> selectDoneTasks(Long userId)
    {
        // 从Flowable查询已办任务
        List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
            .taskAssignee(userId.toString())
            .finished()
            .orderByHistoricTaskInstanceEndTime()
            .desc()
            .list();
        
        List<OaWorkflowTask> doneTasks = new ArrayList<>();
        for (HistoricTaskInstance historicTask : historicTasks) {
            OaWorkflowTask oaTask = convertToOaWorkflowTask(historicTask);
            doneTasks.add(oaTask);
        }

        return doneTasks;
    }

    /**
     * 查询流程实例列表
     * 
     * @param oaWorkflowInstance 流程实例
     * @return 流程实例
     */
    @Override
    public List<OaWorkflowInstance> selectOaWorkflowInstanceList(OaWorkflowInstance oaWorkflowInstance)
    {
        return workflowInstanceMapper.selectOaWorkflowInstanceList(oaWorkflowInstance);
    }

    /**
     * 查询流程实例详情
     * 
     * @param instanceId 实例ID
     * @return 流程实例
     */
    @Override
    public OaWorkflowInstance selectOaWorkflowInstanceByInstanceId(Long instanceId)
    {
        return workflowInstanceMapper.selectOaWorkflowInstanceByInstanceId(instanceId);
    }

    /**
     * 终止流程实例
     * 
     * @param instanceId 实例ID
     * @param reason 终止原因
     * @return 结果
     */
    @Override
    @Transactional
    public boolean terminateProcess(Long instanceId, String reason)
    {
        try {
            OaWorkflowInstance instance = workflowInstanceMapper.selectOaWorkflowInstanceByInstanceId(instanceId);
            if (instance != null && instance.getBusinessKey() != null) {
                // 删除流程实例
                runtimeService.deleteProcessInstance(instance.getBusinessKey(), reason);
                
                // 更新实例状态
                instance.setStatus("3"); // 已终止
                instance.setEndTime(new Date());
                workflowInstanceMapper.updateOaWorkflowInstance(instance);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 转办任务
     * 
     * @param taskId 任务ID
     * @param targetUserId 目标用户ID
     * @param comment 转办说明
     * @return 结果
     */
    @Override
    public boolean delegateTask(String taskId, Long targetUserId, String comment)
    {
        try {
            taskService.delegateTask(taskId, targetUserId.toString());
            
            // 添加转办评论
            if (comment != null && !comment.isEmpty()) {
                taskService.addComment(taskId, null, "转办：" + comment);
            }
            
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取流程图
     * 
     * @param processInstanceId 流程实例ID
     * @return 流程图字节数组
     */
    @Override
    public byte[] getProcessDiagram(String processInstanceId)
    {
        try {
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
            
            if (processInstance != null) {
                InputStream inputStream = repositoryService.getProcessDiagram(processInstance.getProcessDefinitionId());
                if (inputStream != null) {
                    byte[] buffer = new byte[inputStream.available()];
                    inputStream.read(buffer);
                    inputStream.close();
                    return buffer;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 部署流程定义
     * 
     * @param workflowName 流程名称
     * @param workflowKey 流程标识
     * @param bpmnXml BPMN XML内容
     * @return 部署ID
     */
    @Override
    public String deployProcess(String workflowName, String workflowKey, String bpmnXml)
    {
        try {
            Deployment deployment = repositoryService.createDeployment()
                .name(workflowName)
                .addString(workflowKey + ".bpmn20.xml", bpmnXml)
                .deploy();
            return deployment.getId();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    // 其他方法的实现...
    
    /**
     * 转换Task为OaWorkflowTask
     */
    private OaWorkflowTask convertToOaWorkflowTask(Task task) {
        OaWorkflowTask oaTask = new OaWorkflowTask();
        oaTask.setTaskName(task.getName());
        oaTask.setTaskKey(task.getTaskDefinitionKey());
        oaTask.setAssigneeId(task.getAssignee() != null ? Long.valueOf(task.getAssignee()) : null);
        oaTask.setAssigneeName(task.getAssignee());
        oaTask.setStatus("1"); // 待处理
        oaTask.setPriority(task.getPriority());
        oaTask.setDueDate(task.getDueDate());
        oaTask.setCreateTime(task.getCreateTime());
        return oaTask;
    }
    
    /**
     * 转换HistoricTaskInstance为OaWorkflowTask
     */
    private OaWorkflowTask convertToOaWorkflowTask(HistoricTaskInstance historicTask) {
        OaWorkflowTask oaTask = new OaWorkflowTask();
        oaTask.setTaskName(historicTask.getName());
        oaTask.setTaskKey(historicTask.getTaskDefinitionKey());
        oaTask.setAssigneeId(historicTask.getAssignee() != null ? Long.valueOf(historicTask.getAssignee()) : null);
        oaTask.setAssigneeName(historicTask.getAssignee());
        oaTask.setStatus("2"); // 已处理
        oaTask.setPriority(historicTask.getPriority());
        oaTask.setDueDate(historicTask.getDueDate());
        oaTask.setCreateTime(historicTask.getCreateTime());
        oaTask.setCompleteTime(historicTask.getEndTime());
        return oaTask;
    }

    // 实现接口中的其他方法...
    @Override
    public List<OaWorkflowTask> selectProcessHistory(String processInstanceId) {
        // TODO: 实现流程历史查询
        return new ArrayList<>();
    }

    @Override
    public boolean suspendProcessDefinition(String processDefinitionId) {
        // TODO: 实现流程定义挂起
        return false;
    }

    @Override
    public boolean activateProcessDefinition(String processDefinitionId) {
        // TODO: 实现流程定义激活
        return false;
    }

    @Override
    public boolean deleteDeployment(String deploymentId, boolean cascade) {
        // TODO: 实现部署删除
        return false;
    }

    @Override
    public List<Map<String, Object>> selectProcessDefinitionList() {
        // TODO: 实现流程定义列表查询
        return new ArrayList<>();
    }

    @Override
    public OaWorkflowInstance selectOaWorkflowInstanceByBusinessKey(String businessKey) {
        // TODO: 实现根据业务主键查询流程实例
        return null;
    }

    @Override
    public List<OaWorkflowInstance> selectUserProcessInstances(Long userId) {
        // TODO: 实现用户流程实例查询
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getNextAssignees(String taskId) {
        // TODO: 实现下一步处理人查询
        return new ArrayList<>();
    }

    @Override
    public boolean rejectTask(String taskId, String targetActivityId, String comment) {
        // TODO: 实现任务回退
        return false;
    }
}
