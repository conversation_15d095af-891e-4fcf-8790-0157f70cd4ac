package com.base.oa.workflow.service;

import java.util.List;
import java.util.Map;
import com.base.oa.workflow.domain.OaWorkflowDefinition;
import com.base.oa.workflow.domain.OaWorkflowInstance;
import com.base.oa.workflow.domain.OaWorkflowTask;

/**
 * 工作流服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IOaWorkflowService 
{
    /**
     * 查询工作流定义列表
     *
     * @param oaWorkflowDefinition 工作流定义
     * @return 工作流定义集合
     */
    public List<OaWorkflowDefinition> selectOaWorkflowDefinitionList(OaWorkflowDefinition oaWorkflowDefinition);

    /**
     * 查询工作流定义
     *
     * @param workflowId 工作流定义主键
     * @return 工作流定义
     */
    public OaWorkflowDefinition selectOaWorkflowDefinitionByWorkflowId(Long workflowId);

    /**
     * 新增工作流定义
     * 
     * @param oaWorkflowDefinition 工作流定义
     * @return 结果
     */
    public int insertOaWorkflowDefinition(OaWorkflowDefinition oaWorkflowDefinition);

    /**
     * 修改工作流定义
     * 
     * @param oaWorkflowDefinition 工作流定义
     * @return 结果
     */
    public int updateOaWorkflowDefinition(OaWorkflowDefinition oaWorkflowDefinition);

    /**
     * 删除工作流定义
     *
     * @param workflowId 工作流定义主键
     * @return 结果
     */
    public int deleteOaWorkflowDefinitionByWorkflowId(Long workflowId);

    /**
     * 批量删除工作流定义
     *
     * @param workflowIds 工作流定义主键数组
     * @return 结果
     */
    public int deleteOaWorkflowDefinitionByWorkflowIds(Long[] workflowIds);

    /**
     * 启动工作流程
     * 
     * @param workflowKey 流程标识
     * @param businessKey 业务主键
     * @param variables 流程变量
     * @return 流程实例ID
     */
    public String startProcess(String workflowKey, String businessKey, Map<String, Object> variables);

    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @param variables 流程变量
     * @param comment 处理意见
     * @return 结果
     */
    public boolean completeTask(String taskId, Map<String, Object> variables, String comment);

    /**
     * 查询待办任务列表
     * 
     * @param userId 用户ID
     * @return 待办任务集合
     */
    public List<OaWorkflowTask> selectTodoTasks(Long userId);

    /**
     * 查询已办任务列表
     * 
     * @param userId 用户ID
     * @return 已办任务集合
     */
    public List<OaWorkflowTask> selectDoneTasks(Long userId);

    /**
     * 查询流程实例列表
     * 
     * @param oaWorkflowInstance 流程实例
     * @return 流程实例集合
     */
    public List<OaWorkflowInstance> selectOaWorkflowInstanceList(OaWorkflowInstance oaWorkflowInstance);

    /**
     * 查询流程实例详情
     * 
     * @param instanceId 实例ID
     * @return 流程实例
     */
    public OaWorkflowInstance selectOaWorkflowInstanceByInstanceId(Long instanceId);

    /**
     * 终止流程实例
     * 
     * @param instanceId 实例ID
     * @param reason 终止原因
     * @return 结果
     */
    public boolean terminateProcess(Long instanceId, String reason);

    /**
     * 转办任务
     * 
     * @param taskId 任务ID
     * @param targetUserId 目标用户ID
     * @param comment 转办说明
     * @return 结果
     */
    public boolean delegateTask(String taskId, Long targetUserId, String comment);

    /**
     * 获取流程图
     * 
     * @param processInstanceId 流程实例ID
     * @return 流程图字节数组
     */
    public byte[] getProcessDiagram(String processInstanceId);

    /**
     * 查询流程历史
     * 
     * @param processInstanceId 流程实例ID
     * @return 历史任务列表
     */
    public List<OaWorkflowTask> selectProcessHistory(String processInstanceId);

    /**
     * 部署流程定义
     * 
     * @param workflowName 流程名称
     * @param workflowKey 流程标识
     * @param bpmnXml BPMN XML内容
     * @return 部署ID
     */
    public String deployProcess(String workflowName, String workflowKey, String bpmnXml);

    /**
     * 挂起流程定义
     * 
     * @param processDefinitionId 流程定义ID
     * @return 结果
     */
    public boolean suspendProcessDefinition(String processDefinitionId);

    /**
     * 激活流程定义
     * 
     * @param processDefinitionId 流程定义ID
     * @return 结果
     */
    public boolean activateProcessDefinition(String processDefinitionId);

    /**
     * 删除流程部署
     * 
     * @param deploymentId 部署ID
     * @param cascade 是否级联删除
     * @return 结果
     */
    public boolean deleteDeployment(String deploymentId, boolean cascade);

    /**
     * 查询流程定义列表
     * 
     * @return 流程定义列表
     */
    public List<Map<String, Object>> selectProcessDefinitionList();

    /**
     * 根据业务主键查询流程实例
     * 
     * @param businessKey 业务主键
     * @return 流程实例
     */
    public OaWorkflowInstance selectOaWorkflowInstanceByBusinessKey(String businessKey);

    /**
     * 查询用户参与的流程实例
     * 
     * @param userId 用户ID
     * @return 流程实例列表
     */
    public List<OaWorkflowInstance> selectUserProcessInstances(Long userId);

    /**
     * 获取下一步处理人
     * 
     * @param taskId 任务ID
     * @return 处理人列表
     */
    public List<Map<String, Object>> getNextAssignees(String taskId);

    /**
     * 回退任务
     * 
     * @param taskId 任务ID
     * @param targetActivityId 目标活动ID
     * @param comment 回退说明
     * @return 结果
     */
    public boolean rejectTask(String taskId, String targetActivityId, String comment);
}
