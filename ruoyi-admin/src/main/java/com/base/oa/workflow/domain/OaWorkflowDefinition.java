package com.base.oa.workflow.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 工作流定义对象 oa_workflow_definition
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class OaWorkflowDefinition extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 流程ID */
    private Long workflowId;

    /** 流程名称 */
    @Excel(name = "流程名称")
    private String workflowName;

    /** 流程标识 */
    @Excel(name = "流程标识")
    private String workflowKey;

    /** 流程分类 */
    @Excel(name = "流程分类")
    private String workflowCategory;

    /** 流程定义XML */
    private String workflowXml;

    /** 版本号 */
    @Excel(name = "版本号")
    private Integer version;

    /** 状态(0停用1启用) */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用")
    private String status;

    public void setWorkflowId(Long workflowId) 
    {
        this.workflowId = workflowId;
    }

    public Long getWorkflowId() 
    {
        return workflowId;
    }
    public void setWorkflowName(String workflowName) 
    {
        this.workflowName = workflowName;
    }

    public String getWorkflowName() 
    {
        return workflowName;
    }
    public void setWorkflowKey(String workflowKey) 
    {
        this.workflowKey = workflowKey;
    }

    public String getWorkflowKey() 
    {
        return workflowKey;
    }
    public void setWorkflowCategory(String workflowCategory) 
    {
        this.workflowCategory = workflowCategory;
    }

    public String getWorkflowCategory() 
    {
        return workflowCategory;
    }
    public void setWorkflowXml(String workflowXml) 
    {
        this.workflowXml = workflowXml;
    }

    public String getWorkflowXml() 
    {
        return workflowXml;
    }
    public void setVersion(Integer version) 
    {
        this.version = version;
    }

    public Integer getVersion() 
    {
        return version;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("workflowId", getWorkflowId())
            .append("workflowName", getWorkflowName())
            .append("workflowKey", getWorkflowKey())
            .append("workflowCategory", getWorkflowCategory())
            .append("workflowXml", getWorkflowXml())
            .append("version", getVersion())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
