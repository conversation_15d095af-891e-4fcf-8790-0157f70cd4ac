package com.base.oa.workflow.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 工作流任务对象 oa_workflow_task
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class OaWorkflowTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private Long taskId;

    /** 流程实例ID */
    @Excel(name = "流程实例ID")
    private Long instanceId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 任务标识 */
    @Excel(name = "任务标识")
    private String taskKey;

    /** 处理人ID */
    @Excel(name = "处理人ID")
    private Long assigneeId;

    /** 处理人姓名 */
    @Excel(name = "处理人姓名")
    private String assigneeName;

    /** 状态(1待处理2已处理3已转办) */
    @Excel(name = "状态", readConverterExp = "1=待处理,2=已处理,3=已转办")
    private String status;

    /** 优先级 */
    @Excel(name = "优先级")
    private Integer priority;

    /** 到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dueDate;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /** 处理意见 */
    @Excel(name = "处理意见")
    private String comment;

    /** 业务主键 */
    private String businessKey;

    /** 流程名称 */
    private String workflowName;

    /** 发起人姓名 */
    private String startUserName;

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }
    public void setInstanceId(Long instanceId) 
    {
        this.instanceId = instanceId;
    }

    public Long getInstanceId() 
    {
        return instanceId;
    }
    public void setTaskName(String taskName) 
    {
        this.taskName = taskName;
    }

    public String getTaskName() 
    {
        return taskName;
    }
    public void setTaskKey(String taskKey) 
    {
        this.taskKey = taskKey;
    }

    public String getTaskKey() 
    {
        return taskKey;
    }
    public void setAssigneeId(Long assigneeId) 
    {
        this.assigneeId = assigneeId;
    }

    public Long getAssigneeId() 
    {
        return assigneeId;
    }
    public void setAssigneeName(String assigneeName) 
    {
        this.assigneeName = assigneeName;
    }

    public String getAssigneeName() 
    {
        return assigneeName;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setPriority(Integer priority) 
    {
        this.priority = priority;
    }

    public Integer getPriority() 
    {
        return priority;
    }
    public void setDueDate(Date dueDate) 
    {
        this.dueDate = dueDate;
    }

    public Date getDueDate() 
    {
        return dueDate;
    }
    public void setCompleteTime(Date completeTime) 
    {
        this.completeTime = completeTime;
    }

    public Date getCompleteTime() 
    {
        return completeTime;
    }
    public void setComment(String comment) 
    {
        this.comment = comment;
    }

    public String getComment() 
    {
        return comment;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getWorkflowName() {
        return workflowName;
    }

    public void setWorkflowName(String workflowName) {
        this.workflowName = workflowName;
    }

    public String getStartUserName() {
        return startUserName;
    }

    public void setStartUserName(String startUserName) {
        this.startUserName = startUserName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskId", getTaskId())
            .append("instanceId", getInstanceId())
            .append("taskName", getTaskName())
            .append("taskKey", getTaskKey())
            .append("assigneeId", getAssigneeId())
            .append("assigneeName", getAssigneeName())
            .append("status", getStatus())
            .append("priority", getPriority())
            .append("dueDate", getDueDate())
            .append("createTime", getCreateTime())
            .append("completeTime", getCompleteTime())
            .append("comment", getComment())
            .toString();
    }
}
