package com.base.oa.workflow.mapper;

import java.util.List;
import com.base.oa.workflow.domain.OaWorkflowDefinition;

/**
 * 工作流定义Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OaWorkflowDefinitionMapper 
{
    /**
     * 查询工作流定义
     * 
     * @param workflowId 工作流定义主键
     * @return 工作流定义
     */
    public OaWorkflowDefinition selectOaWorkflowDefinitionByWorkflowId(Long workflowId);

    /**
     * 根据流程标识查询工作流定义
     * 
     * @param workflowKey 流程标识
     * @return 工作流定义
     */
    public OaWorkflowDefinition selectOaWorkflowDefinitionByKey(String workflowKey);

    /**
     * 查询工作流定义列表
     * 
     * @param oaWorkflowDefinition 工作流定义
     * @return 工作流定义集合
     */
    public List<OaWorkflowDefinition> selectOaWorkflowDefinitionList(OaWorkflowDefinition oaWorkflowDefinition);

    /**
     * 新增工作流定义
     * 
     * @param oaWorkflowDefinition 工作流定义
     * @return 结果
     */
    public int insertOaWorkflowDefinition(OaWorkflowDefinition oaWorkflowDefinition);

    /**
     * 修改工作流定义
     * 
     * @param oaWorkflowDefinition 工作流定义
     * @return 结果
     */
    public int updateOaWorkflowDefinition(OaWorkflowDefinition oaWorkflowDefinition);

    /**
     * 删除工作流定义
     * 
     * @param workflowId 工作流定义主键
     * @return 结果
     */
    public int deleteOaWorkflowDefinitionByWorkflowId(Long workflowId);

    /**
     * 批量删除工作流定义
     * 
     * @param workflowIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaWorkflowDefinitionByWorkflowIds(Long[] workflowIds);
}
