package com.base.oa.workflow.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 工作流实例对象 oa_workflow_instance
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class OaWorkflowInstance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 实例ID */
    private Long instanceId;

    /** 流程定义ID */
    @Excel(name = "流程定义ID")
    private Long workflowId;

    /** 业务主键 */
    @Excel(name = "业务主键")
    private String businessKey;

    /** 发起人ID */
    @Excel(name = "发起人ID")
    private Long startUserId;

    /** 发起人姓名 */
    @Excel(name = "发起人姓名")
    private String startUserName;

    /** 当前任务 */
    @Excel(name = "当前任务")
    private String currentTask;

    /** 状态(1进行中2已完成3已终止) */
    @Excel(name = "状态", readConverterExp = "1=进行中,2=已完成,3=已终止")
    private String status;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 流程名称 */
    private String workflowName;

    /** 流程分类 */
    private String workflowCategory;

    public void setInstanceId(Long instanceId) 
    {
        this.instanceId = instanceId;
    }

    public Long getInstanceId() 
    {
        return instanceId;
    }
    public void setWorkflowId(Long workflowId) 
    {
        this.workflowId = workflowId;
    }

    public Long getWorkflowId() 
    {
        return workflowId;
    }
    public void setBusinessKey(String businessKey) 
    {
        this.businessKey = businessKey;
    }

    public String getBusinessKey() 
    {
        return businessKey;
    }
    public void setStartUserId(Long startUserId) 
    {
        this.startUserId = startUserId;
    }

    public Long getStartUserId() 
    {
        return startUserId;
    }
    public void setStartUserName(String startUserName) 
    {
        this.startUserName = startUserName;
    }

    public String getStartUserName() 
    {
        return startUserName;
    }
    public void setCurrentTask(String currentTask) 
    {
        this.currentTask = currentTask;
    }

    public String getCurrentTask() 
    {
        return currentTask;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public String getWorkflowName() {
        return workflowName;
    }

    public void setWorkflowName(String workflowName) {
        this.workflowName = workflowName;
    }

    public String getWorkflowCategory() {
        return workflowCategory;
    }

    public void setWorkflowCategory(String workflowCategory) {
        this.workflowCategory = workflowCategory;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("instanceId", getInstanceId())
            .append("workflowId", getWorkflowId())
            .append("businessKey", getBusinessKey())
            .append("startUserId", getStartUserId())
            .append("startUserName", getStartUserName())
            .append("currentTask", getCurrentTask())
            .append("status", getStatus())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("createTime", getCreateTime())
            .toString();
    }
}
