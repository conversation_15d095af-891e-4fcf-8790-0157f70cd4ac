package com.base.oa.workflow.mapper;

import java.util.List;
import com.base.oa.workflow.domain.OaWorkflowInstance;

/**
 * 工作流实例Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OaWorkflowInstanceMapper 
{
    /**
     * 查询工作流实例
     * 
     * @param instanceId 工作流实例主键
     * @return 工作流实例
     */
    public OaWorkflowInstance selectOaWorkflowInstanceByInstanceId(Long instanceId);

    /**
     * 根据业务主键查询工作流实例
     * 
     * @param businessKey 业务主键
     * @return 工作流实例
     */
    public OaWorkflowInstance selectOaWorkflowInstanceByBusinessKey(String businessKey);

    /**
     * 查询工作流实例列表
     * 
     * @param oaWorkflowInstance 工作流实例
     * @return 工作流实例集合
     */
    public List<OaWorkflowInstance> selectOaWorkflowInstanceList(OaWorkflowInstance oaWorkflowInstance);

    /**
     * 查询用户发起的流程实例列表
     * 
     * @param startUserId 发起人ID
     * @return 工作流实例集合
     */
    public List<OaWorkflowInstance> selectOaWorkflowInstanceByStartUserId(Long startUserId);

    /**
     * 新增工作流实例
     * 
     * @param oaWorkflowInstance 工作流实例
     * @return 结果
     */
    public int insertOaWorkflowInstance(OaWorkflowInstance oaWorkflowInstance);

    /**
     * 修改工作流实例
     * 
     * @param oaWorkflowInstance 工作流实例
     * @return 结果
     */
    public int updateOaWorkflowInstance(OaWorkflowInstance oaWorkflowInstance);

    /**
     * 删除工作流实例
     * 
     * @param instanceId 工作流实例主键
     * @return 结果
     */
    public int deleteOaWorkflowInstanceByInstanceId(Long instanceId);

    /**
     * 批量删除工作流实例
     * 
     * @param instanceIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaWorkflowInstanceByInstanceIds(Long[] instanceIds);
}
