package com.base.oa.personal.domain;

import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 个人通讯录对象 oa_personal_contact
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class OaPersonalContact extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 联系人ID */
    private Long contactId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    private String name;

    /** 电话号码 */
    @Excel(name = "电话号码")
    private String phone;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String mobile;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 公司 */
    @Excel(name = "公司")
    private String company;

    /** 职位 */
    @Excel(name = "职位")
    private String position;

    /** 部门 */
    @Excel(name = "部门")
    private String department;

    /** 地址 */
    @Excel(name = "地址")
    private String address;

    /** 邮编 */
    @Excel(name = "邮编")
    private String postcode;

    /** QQ号码 */
    @Excel(name = "QQ号码")
    private String qq;

    /** 微信号 */
    @Excel(name = "微信号")
    private String wechat;

    /** 分组 */
    @Excel(name = "分组")
    private String groupName;

    /** 头像 */
    private String avatar;

    /** 生日 */
    @Excel(name = "生日")
    private String birthday;

    /** 性别(0男1女2未知) */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private String gender;

    /** 标签 */
    @Excel(name = "标签")
    private String tags;

    /** 是否收藏(0否1是) */
    @Excel(name = "是否收藏", readConverterExp = "0=否,1=是")
    private String isFavorite;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    public void setContactId(Long contactId) 
    {
        this.contactId = contactId;
    }

    public Long getContactId() 
    {
        return contactId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setMobile(String mobile) 
    {
        this.mobile = mobile;
    }

    public String getMobile() 
    {
        return mobile;
    }
    public void setEmail(String email) 
    {
        this.email = email;
    }

    public String getEmail() 
    {
        return email;
    }
    public void setCompany(String company) 
    {
        this.company = company;
    }

    public String getCompany() 
    {
        return company;
    }
    public void setPosition(String position) 
    {
        this.position = position;
    }

    public String getPosition() 
    {
        return position;
    }
    public void setDepartment(String department) 
    {
        this.department = department;
    }

    public String getDepartment() 
    {
        return department;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setPostcode(String postcode) 
    {
        this.postcode = postcode;
    }

    public String getPostcode() 
    {
        return postcode;
    }
    public void setQq(String qq) 
    {
        this.qq = qq;
    }

    public String getQq() 
    {
        return qq;
    }
    public void setWechat(String wechat) 
    {
        this.wechat = wechat;
    }

    public String getWechat() 
    {
        return wechat;
    }
    public void setGroupName(String groupName) 
    {
        this.groupName = groupName;
    }

    public String getGroupName() 
    {
        return groupName;
    }
    public void setAvatar(String avatar) 
    {
        this.avatar = avatar;
    }

    public String getAvatar() 
    {
        return avatar;
    }
    public void setBirthday(String birthday) 
    {
        this.birthday = birthday;
    }

    public String getBirthday() 
    {
        return birthday;
    }
    public void setGender(String gender) 
    {
        this.gender = gender;
    }

    public String getGender() 
    {
        return gender;
    }
    public void setTags(String tags) 
    {
        this.tags = tags;
    }

    public String getTags() 
    {
        return tags;
    }
    public void setIsFavorite(String isFavorite) 
    {
        this.isFavorite = isFavorite;
    }

    public String getIsFavorite() 
    {
        return isFavorite;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("contactId", getContactId())
            .append("userId", getUserId())
            .append("name", getName())
            .append("phone", getPhone())
            .append("mobile", getMobile())
            .append("email", getEmail())
            .append("company", getCompany())
            .append("position", getPosition())
            .append("department", getDepartment())
            .append("address", getAddress())
            .append("postcode", getPostcode())
            .append("qq", getQq())
            .append("wechat", getWechat())
            .append("groupName", getGroupName())
            .append("avatar", getAvatar())
            .append("birthday", getBirthday())
            .append("gender", getGender())
            .append("tags", getTags())
            .append("isFavorite", getIsFavorite())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .toString();
    }
}
