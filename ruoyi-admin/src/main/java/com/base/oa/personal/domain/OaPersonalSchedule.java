package com.base.oa.personal.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 个人日程对象 oa_personal_schedule
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class OaPersonalSchedule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日程ID */
    private Long scheduleId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户姓名 */
    @Excel(name = "用户姓名")
    private String userName;

    /** 日程标题 */
    @Excel(name = "日程标题")
    private String title;

    /** 日程内容 */
    @Excel(name = "日程内容")
    private String content;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 地点 */
    @Excel(name = "地点")
    private String location;

    /** 提醒时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提醒时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date remindTime;

    /** 状态(1正常2已完成3已取消) */
    @Excel(name = "状态", readConverterExp = "1=正常,2=已完成,3=已取消")
    private String status;

    /** 重复类型(0不重复1每天2每周3每月4每年) */
    @Excel(name = "重复类型", readConverterExp = "0=不重复,1=每天,2=每周,3=每月,4=每年")
    private String repeatType;

    /** 重要程度(1普通2重要3紧急) */
    @Excel(name = "重要程度", readConverterExp = "1=普通,2=重要,3=紧急")
    private String priority;

    /** 参与人员 */
    @Excel(name = "参与人员")
    private String participants;

    /** 是否全天 */
    @Excel(name = "是否全天", readConverterExp = "0=否,1=是")
    private String isAllDay;

    public void setScheduleId(Long scheduleId) 
    {
        this.scheduleId = scheduleId;
    }

    public Long getScheduleId() 
    {
        return scheduleId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }
    public void setRemindTime(Date remindTime) 
    {
        this.remindTime = remindTime;
    }

    public Date getRemindTime() 
    {
        return remindTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setRepeatType(String repeatType) 
    {
        this.repeatType = repeatType;
    }

    public String getRepeatType() 
    {
        return repeatType;
    }
    public void setPriority(String priority) 
    {
        this.priority = priority;
    }

    public String getPriority() 
    {
        return priority;
    }
    public void setParticipants(String participants) 
    {
        this.participants = participants;
    }

    public String getParticipants() 
    {
        return participants;
    }
    public void setIsAllDay(String isAllDay) 
    {
        this.isAllDay = isAllDay;
    }

    public String getIsAllDay() 
    {
        return isAllDay;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("scheduleId", getScheduleId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("title", getTitle())
            .append("content", getContent())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("location", getLocation())
            .append("remindTime", getRemindTime())
            .append("status", getStatus())
            .append("repeatType", getRepeatType())
            .append("priority", getPriority())
            .append("participants", getParticipants())
            .append("isAllDay", getIsAllDay())
            .append("createTime", getCreateTime())
            .toString();
    }
}
