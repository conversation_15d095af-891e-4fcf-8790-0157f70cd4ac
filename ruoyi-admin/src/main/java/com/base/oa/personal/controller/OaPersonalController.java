package com.base.oa.personal.controller;

import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.poi.ExcelUtil;
import com.base.oa.common.service.FileService;
import com.base.oa.personal.domain.OaPersonalSchedule;
import com.base.oa.personal.domain.OaPersonalContact;
import com.base.oa.personal.domain.OaWorkReport;
import com.base.oa.personal.service.IOaPersonalService;

/**
 * 个人办公Controller
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/oa/personal")
public class OaPersonalController extends BaseController
{
    @Autowired
    private IOaPersonalService oaPersonalService;

    @Autowired
    private FileService fileService;

    // ==================== 个人信息管理 ====================

    /**
     * 获取个人信息
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:profile:view')")
    @GetMapping("/profile")
    public AjaxResult getProfile()
    {
        return success(oaPersonalService.getUserProfile(getUserId()));
    }

    /**
     * 修改个人信息
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:profile:edit')")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/profile")
    public AjaxResult updateProfile(@RequestBody Object userProfile)
    {
        return toAjax(oaPersonalService.updateUserProfile(getUserId(), userProfile));
    }

    /**
     * 上传签名图片
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:signature:upload')")
    @Log(title = "上传签名", businessType = BusinessType.UPDATE)
    @PostMapping("/signature/upload")
    public AjaxResult uploadSignature(@RequestParam("file") MultipartFile file)
    {
        try {
            String filePath = fileService.uploadSignature(file);
            return AjaxResult.success("上传成功").put("filePath", filePath);
        } catch (IOException e) {
            return error("上传失败：" + e.getMessage());
        }
    }

    // ==================== 日程管理 ====================

    /**
     * 查询个人日程列表
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:schedule:list')")
    @GetMapping("/schedule/list")
    public TableDataInfo scheduleList(OaPersonalSchedule oaPersonalSchedule)
    {
        startPage();
        oaPersonalSchedule.setUserId(getUserId());
        List<OaPersonalSchedule> list = oaPersonalService.selectOaPersonalScheduleList(oaPersonalSchedule);
        return getDataTable(list);
    }

    /**
     * 获取个人日程详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:schedule:query')")
    @GetMapping("/schedule/{scheduleId}")
    public AjaxResult getScheduleInfo(@PathVariable("scheduleId") Long scheduleId)
    {
        return success(oaPersonalService.selectOaPersonalScheduleByScheduleId(scheduleId));
    }

    /**
     * 新增个人日程
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:schedule:add')")
    @Log(title = "个人日程", businessType = BusinessType.INSERT)
    @PostMapping("/schedule")
    public AjaxResult addSchedule(@RequestBody OaPersonalSchedule oaPersonalSchedule)
    {
        oaPersonalSchedule.setUserId(getUserId());
        oaPersonalSchedule.setUserName(getUsername());
        return toAjax(oaPersonalService.insertOaPersonalSchedule(oaPersonalSchedule));
    }

    /**
     * 修改个人日程
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:schedule:edit')")
    @Log(title = "个人日程", businessType = BusinessType.UPDATE)
    @PutMapping("/schedule")
    public AjaxResult editSchedule(@RequestBody OaPersonalSchedule oaPersonalSchedule)
    {
        return toAjax(oaPersonalService.updateOaPersonalSchedule(oaPersonalSchedule));
    }

    /**
     * 删除个人日程
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:schedule:remove')")
    @Log(title = "个人日程", businessType = BusinessType.DELETE)
    @DeleteMapping("/schedule/{scheduleIds}")
    public AjaxResult removeSchedule(@PathVariable Long[] scheduleIds)
    {
        return toAjax(oaPersonalService.deleteOaPersonalScheduleByScheduleIds(scheduleIds));
    }

    /**
     * 查询今日日程
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:schedule:list')")
    @GetMapping("/schedule/today")
    public AjaxResult getTodaySchedules()
    {
        List<OaPersonalSchedule> list = oaPersonalService.selectTodaySchedules(getUserId());
        return success(list);
    }

    // ==================== 个人通讯录 ====================

    /**
     * 查询个人通讯录列表
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:contact:list')")
    @GetMapping("/contact/list")
    public TableDataInfo contactList(OaPersonalContact oaPersonalContact)
    {
        startPage();
        oaPersonalContact.setUserId(getUserId());
        List<OaPersonalContact> list = oaPersonalService.selectOaPersonalContactList(oaPersonalContact);
        return getDataTable(list);
    }

    /**
     * 获取个人通讯录详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:contact:query')")
    @GetMapping("/contact/{contactId}")
    public AjaxResult getContactInfo(@PathVariable("contactId") Long contactId)
    {
        return success(oaPersonalService.selectOaPersonalContactByContactId(contactId));
    }

    /**
     * 新增个人通讯录
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:contact:add')")
    @Log(title = "个人通讯录", businessType = BusinessType.INSERT)
    @PostMapping("/contact")
    public AjaxResult addContact(@RequestBody OaPersonalContact oaPersonalContact)
    {
        oaPersonalContact.setUserId(getUserId());
        return toAjax(oaPersonalService.insertOaPersonalContact(oaPersonalContact));
    }

    /**
     * 修改个人通讯录
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:contact:edit')")
    @Log(title = "个人通讯录", businessType = BusinessType.UPDATE)
    @PutMapping("/contact")
    public AjaxResult editContact(@RequestBody OaPersonalContact oaPersonalContact)
    {
        return toAjax(oaPersonalService.updateOaPersonalContact(oaPersonalContact));
    }

    /**
     * 删除个人通讯录
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:contact:remove')")
    @Log(title = "个人通讯录", businessType = BusinessType.DELETE)
    @DeleteMapping("/contact/{contactIds}")
    public AjaxResult removeContact(@PathVariable Long[] contactIds)
    {
        return toAjax(oaPersonalService.deleteOaPersonalContactByContactIds(contactIds));
    }

    /**
     * 查询内部通讯录
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:internal:list')")
    @GetMapping("/internal-contact/list")
    public TableDataInfo internalContactList()
    {
        startPage();
        List<Object> list = oaPersonalService.selectInternalContactList();
        return getDataTable(list);
    }

    // ==================== 工作报告 ====================

    /**
     * 查询工作报告列表
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:report:list')")
    @GetMapping("/report/list")
    public TableDataInfo reportList(OaWorkReport oaWorkReport)
    {
        startPage();
        oaWorkReport.setUserId(getUserId());
        List<OaWorkReport> list = oaPersonalService.selectOaWorkReportList(oaWorkReport);
        return getDataTable(list);
    }

    /**
     * 获取工作报告详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:report:query')")
    @GetMapping("/report/{reportId}")
    public AjaxResult getReportInfo(@PathVariable("reportId") Long reportId)
    {
        return success(oaPersonalService.selectOaWorkReportByReportId(reportId));
    }

    /**
     * 新增工作报告
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:report:add')")
    @Log(title = "工作报告", businessType = BusinessType.INSERT)
    @PostMapping("/report")
    public AjaxResult addReport(@RequestBody OaWorkReport oaWorkReport)
    {
        oaWorkReport.setUserId(getUserId());
        oaWorkReport.setUserName(getUsername());
        return toAjax(oaPersonalService.insertOaWorkReport(oaWorkReport));
    }

    /**
     * 修改工作报告
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:report:edit')")
    @Log(title = "工作报告", businessType = BusinessType.UPDATE)
    @PutMapping("/report")
    public AjaxResult editReport(@RequestBody OaWorkReport oaWorkReport)
    {
        return toAjax(oaPersonalService.updateOaWorkReport(oaWorkReport));
    }

    /**
     * 删除工作报告
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:report:remove')")
    @Log(title = "工作报告", businessType = BusinessType.DELETE)
    @DeleteMapping("/report/{reportIds}")
    public AjaxResult removeReport(@PathVariable Long[] reportIds)
    {
        return toAjax(oaPersonalService.deleteOaWorkReportByReportIds(reportIds));
    }

    /**
     * 提交工作报告
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:report:submit')")
    @Log(title = "提交工作报告", businessType = BusinessType.UPDATE)
    @PostMapping("/report/submit/{reportId}")
    public AjaxResult submitReport(@PathVariable Long reportId)
    {
        return toAjax(oaPersonalService.submitWorkReport(reportId));
    }

    /**
     * 查询下属工作报告
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:report:subordinate')")
    @GetMapping("/report/subordinate")
    public TableDataInfo subordinateReportList(OaWorkReport oaWorkReport)
    {
        startPage();
        List<OaWorkReport> list = oaPersonalService.selectSubordinateReports(getUserId(), oaWorkReport);
        return getDataTable(list);
    }

    /**
     * 导出工作报告
     */
    @PreAuthorize("@ss.hasPermi('oa:personal:report:export')")
    @Log(title = "工作报告", businessType = BusinessType.EXPORT)
    @PostMapping("/report/export")
    public void exportReport(HttpServletResponse response, OaWorkReport oaWorkReport)
    {
        oaWorkReport.setUserId(getUserId());
        List<OaWorkReport> list = oaPersonalService.selectOaWorkReportList(oaWorkReport);
        ExcelUtil<OaWorkReport> util = new ExcelUtil<OaWorkReport>(OaWorkReport.class);
        util.exportExcel(response, list, "工作报告数据");
    }
}
