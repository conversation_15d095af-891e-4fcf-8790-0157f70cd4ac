package com.base.oa.personal.mapper;

import java.util.List;
import com.base.oa.personal.domain.OaPersonalContact;

/**
 * 个人通讯录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OaPersonalContactMapper 
{
    /**
     * 查询个人通讯录
     * 
     * @param contactId 个人通讯录主键
     * @return 个人通讯录
     */
    public OaPersonalContact selectOaPersonalContactByContactId(Long contactId);

    /**
     * 查询个人通讯录列表
     * 
     * @param oaPersonalContact 个人通讯录
     * @return 个人通讯录集合
     */
    public List<OaPersonalContact> selectOaPersonalContactList(OaPersonalContact oaPersonalContact);

    /**
     * 新增个人通讯录
     * 
     * @param oaPersonalContact 个人通讯录
     * @return 结果
     */
    public int insertOaPersonalContact(OaPersonalContact oaPersonalContact);

    /**
     * 修改个人通讯录
     * 
     * @param oaPersonalContact 个人通讯录
     * @return 结果
     */
    public int updateOaPersonalContact(OaPersonalContact oaPersonalContact);

    /**
     * 删除个人通讯录
     * 
     * @param contactId 个人通讯录主键
     * @return 结果
     */
    public int deleteOaPersonalContactByContactId(Long contactId);

    /**
     * 批量删除个人通讯录
     * 
     * @param contactIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaPersonalContactByContactIds(Long[] contactIds);
}
