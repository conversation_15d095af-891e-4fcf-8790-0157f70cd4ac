package com.base.oa.personal.mapper;

import java.util.List;
import com.base.oa.personal.domain.OaPersonalSchedule;
import org.apache.ibatis.annotations.Param;

/**
 * 个人日程Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OaPersonalScheduleMapper 
{
    /**
     * 查询个人日程
     * 
     * @param scheduleId 个人日程主键
     * @return 个人日程
     */
    public OaPersonalSchedule selectOaPersonalScheduleByScheduleId(Long scheduleId);

    /**
     * 查询个人日程列表
     * 
     * @param oaPersonalSchedule 个人日程
     * @return 个人日程集合
     */
    public List<OaPersonalSchedule> selectOaPersonalScheduleList(OaPersonalSchedule oaPersonalSchedule);

    /**
     * 新增个人日程
     * 
     * @param oaPersonalSchedule 个人日程
     * @return 结果
     */
    public int insertOaPersonalSchedule(OaPersonalSchedule oaPersonalSchedule);

    /**
     * 修改个人日程
     * 
     * @param oaPersonalSchedule 个人日程
     * @return 结果
     */
    public int updateOaPersonalSchedule(OaPersonalSchedule oaPersonalSchedule);

    /**
     * 删除个人日程
     * 
     * @param scheduleId 个人日程主键
     * @return 结果
     */
    public int deleteOaPersonalScheduleByScheduleId(Long scheduleId);

    /**
     * 批量删除个人日程
     * 
     * @param scheduleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaPersonalScheduleByScheduleIds(Long[] scheduleIds);

    /**
     * 查询今日日程
     * 
     * @param userId 用户ID
     * @return 今日日程列表
     */
    public List<OaPersonalSchedule> selectTodaySchedules(@Param("userId") Long userId);

    /**
     * 查询本周日程
     * 
     * @param userId 用户ID
     * @return 本周日程列表
     */
    public List<OaPersonalSchedule> selectWeekSchedules(@Param("userId") Long userId);

    /**
     * 查询本月日程
     * 
     * @param userId 用户ID
     * @return 本月日程列表
     */
    public List<OaPersonalSchedule> selectMonthSchedules(@Param("userId") Long userId);
}
