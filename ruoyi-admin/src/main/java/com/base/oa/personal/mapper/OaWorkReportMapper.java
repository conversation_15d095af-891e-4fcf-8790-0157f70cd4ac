package com.base.oa.personal.mapper;

import java.util.List;
import com.base.oa.personal.domain.OaWorkReport;

/**
 * 工作报告Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface OaWorkReportMapper 
{
    /**
     * 查询工作报告
     * 
     * @param reportId 工作报告主键
     * @return 工作报告
     */
    public OaWorkReport selectOaWorkReportByReportId(Long reportId);

    /**
     * 查询工作报告列表
     * 
     * @param oaWorkReport 工作报告
     * @return 工作报告集合
     */
    public List<OaWorkReport> selectOaWorkReportList(OaWorkReport oaWorkReport);

    /**
     * 新增工作报告
     * 
     * @param oaWorkReport 工作报告
     * @return 结果
     */
    public int insertOaWorkReport(OaWorkReport oaWorkReport);

    /**
     * 修改工作报告
     * 
     * @param oaWorkReport 工作报告
     * @return 结果
     */
    public int updateOaWorkReport(OaWorkReport oaWorkReport);

    /**
     * 删除工作报告
     * 
     * @param reportId 工作报告主键
     * @return 结果
     */
    public int deleteOaWorkReportByReportId(Long reportId);

    /**
     * 批量删除工作报告
     * 
     * @param reportIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOaWorkReportByReportIds(Long[] reportIds);
}
