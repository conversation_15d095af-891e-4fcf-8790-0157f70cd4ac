<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <process id="receiveDocumentSpecial" name="收文特办流程" isExecutable="true">
    <documentation>收文特办流程：书记收文 -> 分发给任意人员</documentation>
    
    <startEvent id="startEvent" name="开始"></startEvent>
    
    <!-- 书记收文 -->
    <userTask id="secretaryReceive" name="书记收文" flowable:assignee="${secretary}">
      <documentation>书记接收特办收文</documentation>
    </userTask>
    
    <!-- 分发给任意人员 -->
    <userTask id="distributeToAnyPerson" name="分发给任意人员" flowable:assignee="${assignedPerson}">
      <documentation>书记将收文分发给任意指定人员处理</documentation>
    </userTask>
    
    <!-- 流程结束 -->
    <endEvent id="endEvent" name="结束"></endEvent>
    
    <!-- 流程连线 -->
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="secretaryReceive"></sequenceFlow>
    <sequenceFlow id="flow2" sourceRef="secretaryReceive" targetRef="distributeToAnyPerson"></sequenceFlow>
    <sequenceFlow id="flow3" sourceRef="distributeToAnyPerson" targetRef="endEvent"></sequenceFlow>
  </process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_receiveDocumentSpecial">
    <bpmndi:BPMNPlane bpmnElement="receiveDocumentSpecial" id="BPMNPlane_receiveDocumentSpecial">
      <bpmndi:BPMNShape bpmnElement="startEvent" id="BPMNShape_startEvent">
        <omgdc:Bounds height="30.0" width="30.0" x="100.0" y="163.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="secretaryReceive" id="BPMNShape_secretaryReceive">
        <omgdc:Bounds height="80.0" width="100.0" x="180.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="distributeToAnyPerson" id="BPMNShape_distributeToAnyPerson">
        <omgdc:Bounds height="80.0" width="100.0" x="330.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endEvent" id="BPMNShape_endEvent">
        <omgdc:Bounds height="28.0" width="28.0" x="480.0" y="164.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      
      <!-- 连线定义 -->
      <bpmndi:BPMNEdge bpmnElement="flow1" id="BPMNEdge_flow1">
        <omgdi:waypoint x="130.0" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="180.0" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow2" id="BPMNEdge_flow2">
        <omgdi:waypoint x="280.0" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="330.0" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow3" id="BPMNEdge_flow3">
        <omgdi:waypoint x="430.0" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="480.0" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
