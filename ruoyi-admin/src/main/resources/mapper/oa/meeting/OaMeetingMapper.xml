<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.meeting.mapper.OaMeetingMapper">
    
    <resultMap type="OaMeeting" id="OaMeetingResult">
        <result property="meetingId"    column="meeting_id"    />
        <result property="meetingTitle"    column="meeting_title"    />
        <result property="meetingType"    column="meeting_type"    />
        <result property="roomName"    column="room_name"    />
        <result property="location"    column="location"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="actualStartTime"    column="actual_start_time"    />
        <result property="actualEndTime"    column="actual_end_time"    />
        <result property="hostId"    column="host_id"    />
        <result property="hostName"    column="host_name"    />
        <result property="participants"    column="participants"    />
        <result property="meetingAgenda"    column="meeting_agenda"    />
        <result property="meetingSummary"    column="meeting_summary"    />
        <result property="attachments"    column="attachments"    />
        <result property="status"    column="status"    />
        <result property="isRecurring"    column="is_recurring"    />
        <result property="recurringRule"    column="recurring_rule"    />
        <result property="reminderTime"    column="reminder_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOaMeetingVo">
        select meeting_id, meeting_title, meeting_type, meeting_room, start_time, end_time, actual_start_time, actual_end_time, host_id, host_name, participants, meeting_agenda, meeting_summary, attachments, status, is_recurring, recurring_rule, reminder_time, create_by, create_time, update_by, update_time, remark from oa_meeting
    </sql>

    <select id="selectOaMeetingList" parameterType="OaMeeting" resultMap="OaMeetingResult">
        <include refid="selectOaMeetingVo"/>
        <where>  
            <if test="meetingTitle != null  and meetingTitle != ''"> and meeting_title like concat('%', #{meetingTitle}, '%')</if>
            <if test="meetingType != null  and meetingType != ''"> and meeting_type = #{meetingType}</if>
            <if test="meetingRoom != null  and meetingRoom != ''"> and meeting_room like concat('%', #{meetingRoom}, '%')</if>
            <if test="startTime != null "> and start_time &gt;= #{startTime}</if>
            <if test="endTime != null "> and end_time &lt;= #{endTime}</if>
            <if test="hostId != null "> and host_id = #{hostId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="isRecurring != null "> and is_recurring = #{isRecurring}</if>
        </where>
        order by start_time desc
    </select>
    
    <select id="selectOaMeetingByMeetingId" parameterType="Long" resultMap="OaMeetingResult">
        <include refid="selectOaMeetingVo"/>
        where meeting_id = #{meetingId}
    </select>
        
    <insert id="insertOaMeeting" parameterType="OaMeeting" useGeneratedKeys="true" keyProperty="meetingId">
        insert into oa_meeting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="meetingTitle != null and meetingTitle != ''">meeting_title,</if>
            <if test="meetingType != null">meeting_type,</if>
            <if test="meetingRoom != null">meeting_room,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="actualStartTime != null">actual_start_time,</if>
            <if test="actualEndTime != null">actual_end_time,</if>
            <if test="hostId != null">host_id,</if>
            <if test="hostName != null">host_name,</if>
            <if test="participants != null">participants,</if>
            <if test="meetingAgenda != null">meeting_agenda,</if>
            <if test="meetingSummary != null">meeting_summary,</if>
            <if test="attachments != null">attachments,</if>
            <if test="status != null">status,</if>
            <if test="isRecurring != null">is_recurring,</if>
            <if test="recurringRule != null">recurring_rule,</if>
            <if test="reminderTime != null">reminder_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="meetingTitle != null and meetingTitle != ''">#{meetingTitle},</if>
            <if test="meetingType != null">#{meetingType},</if>
            <if test="meetingRoom != null">#{meetingRoom},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="actualStartTime != null">#{actualStartTime},</if>
            <if test="actualEndTime != null">#{actualEndTime},</if>
            <if test="hostId != null">#{hostId},</if>
            <if test="hostName != null">#{hostName},</if>
            <if test="participants != null">#{participants},</if>
            <if test="meetingAgenda != null">#{meetingAgenda},</if>
            <if test="meetingSummary != null">#{meetingSummary},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="status != null">#{status},</if>
            <if test="isRecurring != null">#{isRecurring},</if>
            <if test="recurringRule != null">#{recurringRule},</if>
            <if test="reminderTime != null">#{reminderTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOaMeeting" parameterType="OaMeeting">
        update oa_meeting
        <trim prefix="SET" suffixOverrides=",">
            <if test="meetingTitle != null and meetingTitle != ''">meeting_title = #{meetingTitle},</if>
            <if test="meetingType != null">meeting_type = #{meetingType},</if>
            <if test="meetingRoom != null">meeting_room = #{meetingRoom},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="actualStartTime != null">actual_start_time = #{actualStartTime},</if>
            <if test="actualEndTime != null">actual_end_time = #{actualEndTime},</if>
            <if test="hostId != null">host_id = #{hostId},</if>
            <if test="hostName != null">host_name = #{hostName},</if>
            <if test="participants != null">participants = #{participants},</if>
            <if test="meetingAgenda != null">meeting_agenda = #{meetingAgenda},</if>
            <if test="meetingSummary != null">meeting_summary = #{meetingSummary},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isRecurring != null">is_recurring = #{isRecurring},</if>
            <if test="recurringRule != null">recurring_rule = #{recurringRule},</if>
            <if test="reminderTime != null">reminder_time = #{reminderTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where meeting_id = #{meetingId}
    </update>

    <delete id="deleteOaMeetingByMeetingId" parameterType="Long">
        delete from oa_meeting where meeting_id = #{meetingId}
    </delete>

    <delete id="deleteOaMeetingByMeetingIds" parameterType="String">
        delete from oa_meeting where meeting_id in 
        <foreach item="meetingId" collection="array" open="(" separator="," close=")">
            #{meetingId}
        </foreach>
    </delete>

</mapper>
