<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.personal.mapper.OaWorkReportMapper">
    
    <resultMap type="OaWorkReport" id="OaWorkReportResult">
        <result property="reportId"    column="report_id"    />
        <result property="reportTitle"    column="report_title"    />
        <result property="reportType"    column="report_type"    />
        <result property="reportDate"    column="report_date"    />
        <result property="reportContent"    column="report_content"    />
        <result property="workSummary"    column="work_summary"    />
        <result property="workPlan"    column="work_plan"    />
        <result property="workIssues"    column="work_issues"    />
        <result property="reporterId"    column="reporter_id"    />
        <result property="reporterName"    column="reporter_name"    />
        <result property="deptId"    column="dept_id"    />
        <result property="status"    column="status"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="reviewerId"    column="reviewer_id"    />
        <result property="reviewerName"    column="reviewer_name"    />
        <result property="reviewTime"    column="review_time"    />
        <result property="reviewComment"    column="review_comment"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOaWorkReportVo">
        select report_id, report_title, report_type, report_date, report_content, work_summary, work_plan, work_issues, reporter_id, reporter_name, dept_id, status, submit_time, reviewer_id, reviewer_name, review_time, review_comment, create_by, create_time, update_by, update_time, remark from oa_work_report
    </sql>

    <select id="selectOaWorkReportList" parameterType="OaWorkReport" resultMap="OaWorkReportResult">
        <include refid="selectOaWorkReportVo"/>
        <where>  
            <if test="reportTitle != null  and reportTitle != ''"> and report_title like concat('%', #{reportTitle}, '%')</if>
            <if test="reportType != null  and reportType != ''"> and report_type = #{reportType}</if>
            <if test="reportDate != null "> and report_date = #{reportDate}</if>
            <if test="reporterId != null "> and reporter_id = #{reporterId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="reviewerId != null "> and reviewer_id = #{reviewerId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOaWorkReportByReportId" parameterType="Long" resultMap="OaWorkReportResult">
        <include refid="selectOaWorkReportVo"/>
        where report_id = #{reportId}
    </select>
        
    <insert id="insertOaWorkReport" parameterType="OaWorkReport" useGeneratedKeys="true" keyProperty="reportId">
        insert into oa_work_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportTitle != null and reportTitle != ''">report_title,</if>
            <if test="reportType != null and reportType != ''">report_type,</if>
            <if test="reportDate != null">report_date,</if>
            <if test="reportContent != null">report_content,</if>
            <if test="workSummary != null">work_summary,</if>
            <if test="workPlan != null">work_plan,</if>
            <if test="workIssues != null">work_issues,</if>
            <if test="reporterId != null">reporter_id,</if>
            <if test="reporterName != null">reporter_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="status != null">status,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="reviewerId != null">reviewer_id,</if>
            <if test="reviewerName != null">reviewer_name,</if>
            <if test="reviewTime != null">review_time,</if>
            <if test="reviewComment != null">review_comment,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportTitle != null and reportTitle != ''">#{reportTitle},</if>
            <if test="reportType != null and reportType != ''">#{reportType},</if>
            <if test="reportDate != null">#{reportDate},</if>
            <if test="reportContent != null">#{reportContent},</if>
            <if test="workSummary != null">#{workSummary},</if>
            <if test="workPlan != null">#{workPlan},</if>
            <if test="workIssues != null">#{workIssues},</if>
            <if test="reporterId != null">#{reporterId},</if>
            <if test="reporterName != null">#{reporterName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="status != null">#{status},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="reviewerId != null">#{reviewerId},</if>
            <if test="reviewerName != null">#{reviewerName},</if>
            <if test="reviewTime != null">#{reviewTime},</if>
            <if test="reviewComment != null">#{reviewComment},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOaWorkReport" parameterType="OaWorkReport">
        update oa_work_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="reportTitle != null and reportTitle != ''">report_title = #{reportTitle},</if>
            <if test="reportType != null and reportType != ''">report_type = #{reportType},</if>
            <if test="reportDate != null">report_date = #{reportDate},</if>
            <if test="reportContent != null">report_content = #{reportContent},</if>
            <if test="workSummary != null">work_summary = #{workSummary},</if>
            <if test="workPlan != null">work_plan = #{workPlan},</if>
            <if test="workIssues != null">work_issues = #{workIssues},</if>
            <if test="reporterId != null">reporter_id = #{reporterId},</if>
            <if test="reporterName != null">reporter_name = #{reporterName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="reviewerId != null">reviewer_id = #{reviewerId},</if>
            <if test="reviewerName != null">reviewer_name = #{reviewerName},</if>
            <if test="reviewTime != null">review_time = #{reviewTime},</if>
            <if test="reviewComment != null">review_comment = #{reviewComment},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where report_id = #{reportId}
    </update>

    <delete id="deleteOaWorkReportByReportId" parameterType="Long">
        delete from oa_work_report where report_id = #{reportId}
    </delete>

    <delete id="deleteOaWorkReportByReportIds" parameterType="String">
        delete from oa_work_report where report_id in 
        <foreach item="reportId" collection="array" open="(" separator="," close=")">
            #{reportId}
        </foreach>
    </delete>

</mapper>
