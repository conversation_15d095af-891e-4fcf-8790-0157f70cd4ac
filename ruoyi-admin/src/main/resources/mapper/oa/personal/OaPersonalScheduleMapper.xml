<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.personal.mapper.OaPersonalScheduleMapper">
    
    <resultMap type="com.base.oa.personal.domain.OaPersonalSchedule" id="OaPersonalScheduleResult">
        <result property="scheduleId"    column="schedule_id"    />
        <result property="userId"        column="user_id"        />
        <result property="userName"      column="user_name"      />
        <result property="title"         column="title"          />
        <result property="content"       column="content"        />
        <result property="startTime"     column="start_time"     />
        <result property="endTime"       column="end_time"       />
        <result property="location"      column="location"       />
        <result property="remindTime"    column="remind_time"    />
        <result property="status"        column="status"         />
        <result property="repeatType"    column="repeat_type"    />
        <result property="priority"      column="priority"       />
        <result property="participants"  column="participants"   />
        <result property="isAllDay"      column="is_all_day"     />
        <result property="createBy"      column="create_by"      />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"      column="update_by"      />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"        column="remark"         />
    </resultMap>

    <sql id="selectOaPersonalScheduleVo">
        select schedule_id, user_id, user_name, title, content, start_time, end_time, location, remind_time, status, repeat_type, priority, participants, is_all_day, create_by, create_time, update_by, update_time, remark from oa_personal_schedule
    </sql>

    <select id="selectOaPersonalScheduleList" parameterType="com.base.oa.personal.domain.OaPersonalSchedule" resultMap="OaPersonalScheduleResult">
        <include refid="selectOaPersonalScheduleVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="startTime != null "> and start_time &gt;= #{startTime}</if>
            <if test="endTime != null "> and end_time &lt;= #{endTime}</if>
            <if test="location != null  and location != ''"> and location like concat('%', #{location}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by start_time desc
    </select>
    
    <select id="selectOaPersonalScheduleByScheduleId" parameterType="Long" resultMap="OaPersonalScheduleResult">
        <include refid="selectOaPersonalScheduleVo"/>
        where schedule_id = #{scheduleId}
    </select>
        
    <insert id="insertOaPersonalSchedule" parameterType="com.base.oa.personal.domain.OaPersonalSchedule" useGeneratedKeys="true" keyProperty="scheduleId">
        insert into oa_personal_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="location != null">location,</if>
            <if test="remindTime != null">remind_time,</if>
            <if test="status != null">status,</if>
            <if test="repeatType != null">repeat_type,</if>
            <if test="priority != null">priority,</if>
            <if test="participants != null">participants,</if>
            <if test="isAllDay != null">is_all_day,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="location != null">#{location},</if>
            <if test="remindTime != null">#{remindTime},</if>
            <if test="status != null">#{status},</if>
            <if test="repeatType != null">#{repeatType},</if>
            <if test="priority != null">#{priority},</if>
            <if test="participants != null">#{participants},</if>
            <if test="isAllDay != null">#{isAllDay},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOaPersonalSchedule" parameterType="com.base.oa.personal.domain.OaPersonalSchedule">
        update oa_personal_schedule
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="location != null">location = #{location},</if>
            <if test="remindTime != null">remind_time = #{remindTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="repeatType != null">repeat_type = #{repeatType},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="participants != null">participants = #{participants},</if>
            <if test="isAllDay != null">is_all_day = #{isAllDay},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where schedule_id = #{scheduleId}
    </update>

    <delete id="deleteOaPersonalScheduleByScheduleId" parameterType="Long">
        delete from oa_personal_schedule where schedule_id = #{scheduleId}
    </delete>

    <delete id="deleteOaPersonalScheduleByScheduleIds" parameterType="String">
        delete from oa_personal_schedule where schedule_id in 
        <foreach item="scheduleId" collection="array" open="(" separator="," close=")">
            #{scheduleId}
        </foreach>
    </delete>

    <select id="selectTodaySchedules" parameterType="Long" resultMap="OaPersonalScheduleResult">
        <include refid="selectOaPersonalScheduleVo"/>
        where user_id = #{userId} 
        and DATE(start_time) = CURDATE()
        order by start_time
    </select>

    <select id="selectWeekSchedules" parameterType="Long" resultMap="OaPersonalScheduleResult">
        <include refid="selectOaPersonalScheduleVo"/>
        where user_id = #{userId} 
        and YEARWEEK(start_time) = YEARWEEK(NOW())
        order by start_time
    </select>

    <select id="selectMonthSchedules" parameterType="Long" resultMap="OaPersonalScheduleResult">
        <include refid="selectOaPersonalScheduleVo"/>
        where user_id = #{userId} 
        and YEAR(start_time) = YEAR(NOW()) 
        and MONTH(start_time) = MONTH(NOW())
        order by start_time
    </select>
</mapper>
