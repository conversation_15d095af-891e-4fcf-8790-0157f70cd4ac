<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.personal.mapper.OaPersonalContactMapper">
    
    <resultMap type="OaPersonalContact" id="OaPersonalContactResult">
        <result property="contactId"    column="contact_id"    />
        <result property="contactName"    column="contact_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactEmail"    column="contact_email"    />
        <result property="contactCompany"    column="contact_company"    />
        <result property="contactPosition"    column="contact_position"    />
        <result property="contactAddress"    column="contact_address"    />
        <result property="contactType"    column="contact_type"    />
        <result property="isInternal"    column="is_internal"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="avatar"    column="avatar"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOaPersonalContactVo">
        select contact_id, contact_name, contact_phone, contact_email, contact_company, contact_position, contact_address, contact_type, is_internal, dept_id, user_id, avatar, remark, create_by, create_time, update_by, update_time from oa_personal_contact
    </sql>

    <select id="selectOaPersonalContactList" parameterType="OaPersonalContact" resultMap="OaPersonalContactResult">
        <include refid="selectOaPersonalContactVo"/>
        <where>  
            <if test="contactName != null  and contactName != ''"> and contact_name like concat('%', #{contactName}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone like concat('%', #{contactPhone}, '%')</if>
            <if test="contactEmail != null  and contactEmail != ''"> and contact_email like concat('%', #{contactEmail}, '%')</if>
            <if test="contactCompany != null  and contactCompany != ''"> and contact_company like concat('%', #{contactCompany}, '%')</if>
            <if test="contactType != null  and contactType != ''"> and contact_type = #{contactType}</if>
            <if test="isInternal != null "> and is_internal = #{isInternal}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOaPersonalContactByContactId" parameterType="Long" resultMap="OaPersonalContactResult">
        <include refid="selectOaPersonalContactVo"/>
        where contact_id = #{contactId}
    </select>
        
    <insert id="insertOaPersonalContact" parameterType="OaPersonalContact" useGeneratedKeys="true" keyProperty="contactId">
        insert into oa_personal_contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contactName != null and contactName != ''">contact_name,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="contactCompany != null">contact_company,</if>
            <if test="contactPosition != null">contact_position,</if>
            <if test="contactAddress != null">contact_address,</if>
            <if test="contactType != null">contact_type,</if>
            <if test="isInternal != null">is_internal,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="avatar != null">avatar,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contactName != null and contactName != ''">#{contactName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="contactCompany != null">#{contactCompany},</if>
            <if test="contactPosition != null">#{contactPosition},</if>
            <if test="contactAddress != null">#{contactAddress},</if>
            <if test="contactType != null">#{contactType},</if>
            <if test="isInternal != null">#{isInternal},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOaPersonalContact" parameterType="OaPersonalContact">
        update oa_personal_contact
        <trim prefix="SET" suffixOverrides=",">
            <if test="contactName != null and contactName != ''">contact_name = #{contactName},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="contactCompany != null">contact_company = #{contactCompany},</if>
            <if test="contactPosition != null">contact_position = #{contactPosition},</if>
            <if test="contactAddress != null">contact_address = #{contactAddress},</if>
            <if test="contactType != null">contact_type = #{contactType},</if>
            <if test="isInternal != null">is_internal = #{isInternal},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where contact_id = #{contactId}
    </update>

    <delete id="deleteOaPersonalContactByContactId" parameterType="Long">
        delete from oa_personal_contact where contact_id = #{contactId}
    </delete>

    <delete id="deleteOaPersonalContactByContactIds" parameterType="String">
        delete from oa_personal_contact where contact_id in 
        <foreach item="contactId" collection="array" open="(" separator="," close=")">
            #{contactId}
        </foreach>
    </delete>

</mapper>
