<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.document.mapper.OaDocumentSendMapper">
    
    <resultMap type="OaDocumentSend" id="OaDocumentSendResult">
        <result property="docId"    column="doc_id"    />
        <result property="docNumber"    column="doc_number"    />
        <result property="docTitle"    column="doc_title"    />
        <result property="docType"    column="doc_type"    />
        <result property="receiverUnit"    column="receiver_unit"    />
        <result property="receiverContact"    column="receiver_contact"    />
        <result property="sendDate"    column="send_date"    />
        <result property="urgencyLevel"    column="urgency_level"    />
        <result property="securityLevel"    column="security_level"    />
        <result property="docContent"    column="doc_content"    />
        <result property="attachments"    column="attachments"    />
        <result property="drafterId"    column="drafter_id"    />
        <result property="drafterName"    column="drafter_name"    />
        <result property="draftTime"    column="draft_time"    />
        <result property="status"    column="status"    />
        <result property="workflowInstanceId"    column="workflow_instance_id"    />
        <result property="currentStep"    column="current_step"    />
        <result property="currentAssignee"    column="current_assignee"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOaDocumentSendVo">
        select doc_id, doc_number, doc_title, doc_type, receiver_unit, receiver_contact, send_date, urgency_level, security_level, doc_content, attachments, drafter_id, drafter_name, draft_time, status, workflow_instance_id, current_step, current_assignee, create_by, create_time, update_by, update_time, remark from oa_document_send
    </sql>

    <select id="selectOaDocumentSendList" parameterType="OaDocumentSend" resultMap="OaDocumentSendResult">
        <include refid="selectOaDocumentSendVo"/>
        <where>  
            <if test="docNumber != null  and docNumber != ''"> and doc_number like concat('%', #{docNumber}, '%')</if>
            <if test="docTitle != null  and docTitle != ''"> and doc_title like concat('%', #{docTitle}, '%')</if>
            <if test="docType != null  and docType != ''"> and doc_type = #{docType}</if>
            <if test="receiverUnit != null  and receiverUnit != ''"> and receiver_unit like concat('%', #{receiverUnit}, '%')</if>
            <if test="sendDate != null "> and send_date = #{sendDate}</if>
            <if test="urgencyLevel != null  and urgencyLevel != ''"> and urgency_level = #{urgencyLevel}</if>
            <if test="securityLevel != null  and securityLevel != ''"> and security_level = #{securityLevel}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="currentAssignee != null  and currentAssignee != ''"> and current_assignee = #{currentAssignee}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOaDocumentSendByDocId" parameterType="Long" resultMap="OaDocumentSendResult">
        <include refid="selectOaDocumentSendVo"/>
        where doc_id = #{docId}
    </select>
        
    <insert id="insertOaDocumentSend" parameterType="OaDocumentSend" useGeneratedKeys="true" keyProperty="docId">
        insert into oa_document_send
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="docNumber != null and docNumber != ''">doc_number,</if>
            <if test="docTitle != null and docTitle != ''">doc_title,</if>
            <if test="docType != null">doc_type,</if>
            <if test="receiverUnit != null">receiver_unit,</if>
            <if test="receiverContact != null">receiver_contact,</if>
            <if test="sendDate != null">send_date,</if>
            <if test="urgencyLevel != null">urgency_level,</if>
            <if test="securityLevel != null">security_level,</if>
            <if test="docContent != null">doc_content,</if>
            <if test="attachments != null">attachments,</if>
            <if test="drafterId != null">drafter_id,</if>
            <if test="drafterName != null">drafter_name,</if>
            <if test="draftTime != null">draft_time,</if>
            <if test="status != null">status,</if>
            <if test="workflowInstanceId != null">workflow_instance_id,</if>
            <if test="currentStep != null">current_step,</if>
            <if test="currentAssignee != null">current_assignee,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="docNumber != null and docNumber != ''">#{docNumber},</if>
            <if test="docTitle != null and docTitle != ''">#{docTitle},</if>
            <if test="docType != null">#{docType},</if>
            <if test="receiverUnit != null">#{receiverUnit},</if>
            <if test="receiverContact != null">#{receiverContact},</if>
            <if test="sendDate != null">#{sendDate},</if>
            <if test="urgencyLevel != null">#{urgencyLevel},</if>
            <if test="securityLevel != null">#{securityLevel},</if>
            <if test="docContent != null">#{docContent},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="drafterId != null">#{drafterId},</if>
            <if test="drafterName != null">#{drafterName},</if>
            <if test="draftTime != null">#{draftTime},</if>
            <if test="status != null">#{status},</if>
            <if test="workflowInstanceId != null">#{workflowInstanceId},</if>
            <if test="currentStep != null">#{currentStep},</if>
            <if test="currentAssignee != null">#{currentAssignee},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOaDocumentSend" parameterType="OaDocumentSend">
        update oa_document_send
        <trim prefix="SET" suffixOverrides=",">
            <if test="docNumber != null and docNumber != ''">doc_number = #{docNumber},</if>
            <if test="docTitle != null and docTitle != ''">doc_title = #{docTitle},</if>
            <if test="docType != null">doc_type = #{docType},</if>
            <if test="receiverUnit != null">receiver_unit = #{receiverUnit},</if>
            <if test="receiverContact != null">receiver_contact = #{receiverContact},</if>
            <if test="sendDate != null">send_date = #{sendDate},</if>
            <if test="urgencyLevel != null">urgency_level = #{urgencyLevel},</if>
            <if test="securityLevel != null">security_level = #{securityLevel},</if>
            <if test="docContent != null">doc_content = #{docContent},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="drafterId != null">drafter_id = #{drafterId},</if>
            <if test="drafterName != null">drafter_name = #{drafterName},</if>
            <if test="draftTime != null">draft_time = #{draftTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="workflowInstanceId != null">workflow_instance_id = #{workflowInstanceId},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="currentAssignee != null">current_assignee = #{currentAssignee},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where doc_id = #{docId}
    </update>

    <delete id="deleteOaDocumentSendByDocId" parameterType="Long">
        delete from oa_document_send where doc_id = #{docId}
    </delete>

    <delete id="deleteOaDocumentSendByDocIds" parameterType="String">
        delete from oa_document_send where doc_id in 
        <foreach item="docId" collection="array" open="(" separator="," close=")">
            #{docId}
        </foreach>
    </delete>

</mapper>
