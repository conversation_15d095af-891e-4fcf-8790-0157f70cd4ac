<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.workflow.mapper.OaWorkflowDefinitionMapper">
    
    <resultMap type="OaWorkflowDefinition" id="OaWorkflowDefinitionResult">
        <result property="definitionId"    column="definition_id"    />
        <result property="definitionKey"    column="definition_key"    />
        <result property="definitionName"    column="definition_name"    />
        <result property="category"    column="category"    />
        <result property="version"    column="version"    />
        <result property="deploymentId"    column="deployment_id"    />
        <result property="resourceName"    column="resource_name"    />
        <result property="description"    column="description"    />
        <result property="formKey"    column="form_key"    />
        <result property="suspended"    column="suspended"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOaWorkflowDefinitionVo">
        select definition_id, definition_key, definition_name, category, version, deployment_id, resource_name, description, form_key, suspended, create_by, create_time, update_by, update_time, remark from oa_workflow_definition
    </sql>

    <select id="selectOaWorkflowDefinitionList" parameterType="OaWorkflowDefinition" resultMap="OaWorkflowDefinitionResult">
        <include refid="selectOaWorkflowDefinitionVo"/>
        <where>  
            <if test="definitionKey != null  and definitionKey != ''"> and definition_key = #{definitionKey}</if>
            <if test="definitionName != null  and definitionName != ''"> and definition_name like concat('%', #{definitionName}, '%')</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="suspended != null "> and suspended = #{suspended}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOaWorkflowDefinitionByDefinitionId" parameterType="Long" resultMap="OaWorkflowDefinitionResult">
        <include refid="selectOaWorkflowDefinitionVo"/>
        where definition_id = #{definitionId}
    </select>

    <select id="selectOaWorkflowDefinitionByWorkflowId" parameterType="Long" resultMap="OaWorkflowDefinitionResult">
        <include refid="selectOaWorkflowDefinitionVo"/>
        where definition_id = #{workflowId}
    </select>

    <select id="selectOaWorkflowDefinitionByKey" parameterType="String" resultMap="OaWorkflowDefinitionResult">
        <include refid="selectOaWorkflowDefinitionVo"/>
        where definition_key = #{workflowKey}
    </select>
        
    <insert id="insertOaWorkflowDefinition" parameterType="OaWorkflowDefinition" useGeneratedKeys="true" keyProperty="definitionId">
        insert into oa_workflow_definition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="definitionKey != null and definitionKey != ''">definition_key,</if>
            <if test="definitionName != null and definitionName != ''">definition_name,</if>
            <if test="category != null">category,</if>
            <if test="version != null">version,</if>
            <if test="deploymentId != null and deploymentId != ''">deployment_id,</if>
            <if test="resourceName != null">resource_name,</if>
            <if test="description != null">description,</if>
            <if test="formKey != null">form_key,</if>
            <if test="suspended != null">suspended,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="definitionKey != null and definitionKey != ''">#{definitionKey},</if>
            <if test="definitionName != null and definitionName != ''">#{definitionName},</if>
            <if test="category != null">#{category},</if>
            <if test="version != null">#{version},</if>
            <if test="deploymentId != null and deploymentId != ''">#{deploymentId},</if>
            <if test="resourceName != null">#{resourceName},</if>
            <if test="description != null">#{description},</if>
            <if test="formKey != null">#{formKey},</if>
            <if test="suspended != null">#{suspended},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOaWorkflowDefinition" parameterType="OaWorkflowDefinition">
        update oa_workflow_definition
        <trim prefix="SET" suffixOverrides=",">
            <if test="definitionKey != null and definitionKey != ''">definition_key = #{definitionKey},</if>
            <if test="definitionName != null and definitionName != ''">definition_name = #{definitionName},</if>
            <if test="category != null">category = #{category},</if>
            <if test="version != null">version = #{version},</if>
            <if test="deploymentId != null and deploymentId != ''">deployment_id = #{deploymentId},</if>
            <if test="resourceName != null">resource_name = #{resourceName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="formKey != null">form_key = #{formKey},</if>
            <if test="suspended != null">suspended = #{suspended},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where definition_id = #{definitionId}
    </update>

    <delete id="deleteOaWorkflowDefinitionByDefinitionId" parameterType="Long">
        delete from oa_workflow_definition where definition_id = #{definitionId}
    </delete>

    <delete id="deleteOaWorkflowDefinitionByDefinitionIds" parameterType="String">
        delete from oa_workflow_definition where definition_id in 
        <foreach item="definitionId" collection="array" open="(" separator="," close=")">
            #{definitionId}
        </foreach>
    </delete>

</mapper>
