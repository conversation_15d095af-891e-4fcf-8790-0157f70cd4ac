<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.workflow.mapper.OaWorkflowTaskMapper">
    
    <resultMap type="OaWorkflowTask" id="OaWorkflowTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="flowableTaskId"    column="flowable_task_id"    />
        <result property="instanceId"    column="instance_id"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="taskKey"    column="task_key"    />
        <result property="assignee"    column="assignee"    />
        <result property="candidateUsers"    column="candidate_users"    />
        <result property="candidateGroups"    column="candidate_groups"    />
        <result property="status"    column="status"    />
        <result property="priority"    column="priority"    />
        <result property="dueDate"    column="due_date"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="duration"    column="duration"    />
        <result property="comment"    column="comment"    />
        <result property="formData"    column="form_data"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOaWorkflowTaskVo">
        select task_id, flowable_task_id, instance_id, process_instance_id, task_name, task_key, assignee, candidate_users, candidate_groups, status, priority, due_date, start_time, end_time, duration, comment, form_data, create_by, create_time, update_by, update_time, remark from oa_workflow_task
    </sql>

    <select id="selectOaWorkflowTaskList" parameterType="OaWorkflowTask" resultMap="OaWorkflowTaskResult">
        <include refid="selectOaWorkflowTaskVo"/>
        <where>  
            <if test="flowableTaskId != null  and flowableTaskId != ''"> and flowable_task_id = #{flowableTaskId}</if>
            <if test="instanceId != null "> and instance_id = #{instanceId}</if>
            <if test="processInstanceId != null  and processInstanceId != ''"> and process_instance_id = #{processInstanceId}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskKey != null  and taskKey != ''"> and task_key = #{taskKey}</if>
            <if test="assignee != null  and assignee != ''"> and assignee = #{assignee}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="priority != null "> and priority = #{priority}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOaWorkflowTaskByTaskId" parameterType="Long" resultMap="OaWorkflowTaskResult">
        <include refid="selectOaWorkflowTaskVo"/>
        where task_id = #{taskId}
    </select>
        
    <insert id="insertOaWorkflowTask" parameterType="OaWorkflowTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into oa_workflow_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flowableTaskId != null and flowableTaskId != ''">flowable_task_id,</if>
            <if test="instanceId != null">instance_id,</if>
            <if test="processInstanceId != null and processInstanceId != ''">process_instance_id,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="taskKey != null and taskKey != ''">task_key,</if>
            <if test="assignee != null">assignee,</if>
            <if test="candidateUsers != null">candidate_users,</if>
            <if test="candidateGroups != null">candidate_groups,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="priority != null">priority,</if>
            <if test="dueDate != null">due_date,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="duration != null">duration,</if>
            <if test="comment != null">comment,</if>
            <if test="formData != null">form_data,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flowableTaskId != null and flowableTaskId != ''">#{flowableTaskId},</if>
            <if test="instanceId != null">#{instanceId},</if>
            <if test="processInstanceId != null and processInstanceId != ''">#{processInstanceId},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="taskKey != null and taskKey != ''">#{taskKey},</if>
            <if test="assignee != null">#{assignee},</if>
            <if test="candidateUsers != null">#{candidateUsers},</if>
            <if test="candidateGroups != null">#{candidateGroups},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="priority != null">#{priority},</if>
            <if test="dueDate != null">#{dueDate},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="duration != null">#{duration},</if>
            <if test="comment != null">#{comment},</if>
            <if test="formData != null">#{formData},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOaWorkflowTask" parameterType="OaWorkflowTask">
        update oa_workflow_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="flowableTaskId != null and flowableTaskId != ''">flowable_task_id = #{flowableTaskId},</if>
            <if test="instanceId != null">instance_id = #{instanceId},</if>
            <if test="processInstanceId != null and processInstanceId != ''">process_instance_id = #{processInstanceId},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="taskKey != null and taskKey != ''">task_key = #{taskKey},</if>
            <if test="assignee != null">assignee = #{assignee},</if>
            <if test="candidateUsers != null">candidate_users = #{candidateUsers},</if>
            <if test="candidateGroups != null">candidate_groups = #{candidateGroups},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="dueDate != null">due_date = #{dueDate},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="comment != null">comment = #{comment},</if>
            <if test="formData != null">form_data = #{formData},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteOaWorkflowTaskByTaskId" parameterType="Long">
        delete from oa_workflow_task where task_id = #{taskId}
    </delete>

    <delete id="deleteOaWorkflowTaskByTaskIds" parameterType="String">
        delete from oa_workflow_task where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>

</mapper>
