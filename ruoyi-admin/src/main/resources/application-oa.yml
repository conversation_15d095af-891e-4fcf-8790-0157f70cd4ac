# OA系统配置
oa:
  # 文件存储配置
  file:
    # 上传路径
    upload-path: /opt/oa/upload
    # 允许上传的文件类型
    allowed-extensions: 
      - jpg
      - jpeg
      - png
      - gif
      - bmp
      - pdf
      - doc
      - docx
      - xls
      - xlsx
      - ppt
      - pptx
      - txt
      - zip
      - rar
    # 最大文件大小(MB)
    max-file-size: 100
    # 最大请求大小(MB)
    max-request-size: 500
  
  # 工作流配置
  workflow:
    # 是否启用工作流
    enabled: true
    # 流程图字体
    activity-font-name: 宋体
    label-font-name: 宋体
    annotation-font-name: 宋体
    # 流程部署路径
    deployment-path: classpath*:/processes/
  
  # 公文配置
  document:
    # 公文编号前缀
    number-prefix: 红管
    # 公文编号格式：前缀+年份+流水号
    number-format: "{prefix}〔{year}〕{serial}号"
    # 红头文件模板路径
    template-path: /opt/oa/templates
    # PDF生成配置
    pdf:
      # 字体路径
      font-path: /opt/oa/fonts/simsun.ttf
      # 页面大小
      page-size: A4
      # 页边距
      margin-top: 20
      margin-bottom: 20
      margin-left: 25
      margin-right: 25
  
  # 印章配置
  seal:
    # 印章图片存储路径
    image-path: /opt/oa/seal
    # 印章使用申请编号前缀
    application-prefix: YZ
    # 申请编号格式
    application-format: "{prefix}{year}{month}{day}{serial}"
  
  # 会议配置
  meeting:
    # 会议室预约提前时间(小时)
    advance-booking-hours: 2
    # 会议提醒时间(分钟)
    default-remind-minutes: 30
    # 会议室冲突检查
    conflict-check: true
  
  # 通知配置
  notification:
    # 是否启用邮件通知
    email-enabled: false
    # 是否启用短信通知
    sms-enabled: false
    # 是否启用系统内通知
    system-enabled: true
  
  # 统计配置
  statistics:
    # 是否启用统计功能
    enabled: true
    # 统计数据保留天数
    retention-days: 365
    # 统计报表生成时间(cron表达式)
    report-cron: "0 0 1 * * ?"

# Flowable配置
flowable:
  # 数据库配置 - 使用true进行自动更新
  database-schema-update: true
  db-history-used: true
  history-level: full
  # 检查流程定义
  check-process-definitions: false
  # 异步执行器
  async-executor-activate: false
  # 自动部署流程定义
  deployment-mode: default
  # 邮件服务器配置
  mail-server-host: localhost
  mail-server-port: 25
  mail-server-username:
  mail-server-password:
  # 流程图配置
  activity-font-name: 宋体
  label-font-name: 宋体
  annotation-font-name: 宋体

# 文件上传配置
spring:
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 500MB

# 日志配置
logging:
  level:
    com.base.oa: debug
    org.flowable: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
