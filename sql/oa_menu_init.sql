-- OA系统菜单初始化脚本
-- 删除已存在的OA菜单
DELETE FROM sys_menu WHERE menu_name LIKE '%OA%' OR path LIKE '%oa%';

-- 插入OA主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('OA办公', 0, 1, 'oa', NULL, 1, 0, 'M', '0', '0', NULL, 'office-building', 'admin', sysdate(), 'admin', sysdate(), 'OA办公自动化系统');

-- 获取OA主菜单ID
SET @oa_menu_id = LAST_INSERT_ID();

-- 插入OA子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
-- 工作台
('工作台', @oa_menu_id, 1, 'dashboard', 'oa/dashboard/index', 1, 0, 'C', '0', '0', 'oa:dashboard:view', 'dashboard', 'admin', sysdate(), 'admin', sysdate(), 'OA工作台'),

-- 工作流管理
('工作流管理', @oa_menu_id, 2, 'workflow', NULL, 1, 0, 'M', '0', '0', NULL, 'tree', 'admin', sysdate(), 'admin', sysdate(), '工作流管理'),

-- 公文管理
('公文管理', @oa_menu_id, 3, 'document', NULL, 1, 0, 'M', '0', '0', NULL, 'documentation', 'admin', sysdate(), 'admin', sysdate(), '公文管理'),

-- 个人办公
('个人办公', @oa_menu_id, 4, 'personal', NULL, 1, 0, 'M', '0', '0', NULL, 'user', 'admin', sysdate(), 'admin', sysdate(), '个人办公'),

-- 会议管理
('会议管理', @oa_menu_id, 5, 'meeting', NULL, 1, 0, 'M', '0', '0', NULL, 'peoples', 'admin', sysdate(), 'admin', sysdate(), '会议管理'),

-- 印章管理
('印章管理', @oa_menu_id, 6, 'seal', NULL, 1, 0, 'M', '0', '0', NULL, 'stamp', 'admin', sysdate(), 'admin', sysdate(), '印章管理');

-- 获取子菜单ID
SET @workflow_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '工作流管理' AND parent_id = @oa_menu_id);
SET @document_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '公文管理' AND parent_id = @oa_menu_id);
SET @personal_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '个人办公' AND parent_id = @oa_menu_id);
SET @meeting_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '会议管理' AND parent_id = @oa_menu_id);
SET @seal_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '印章管理' AND parent_id = @oa_menu_id);

-- 工作流子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('流程定义', @workflow_menu_id, 1, 'definition', 'oa/workflow/definition/index', 1, 0, 'C', '0', '0', 'oa:workflow:definition:list', 'tree-table', 'admin', sysdate(), 'admin', sysdate(), '流程定义管理'),
('流程实例', @workflow_menu_id, 2, 'instance', 'oa/workflow/instance/index', 1, 0, 'C', '0', '0', 'oa:workflow:instance:list', 'list', 'admin', sysdate(), 'admin', sysdate(), '流程实例管理'),
('待办任务', @workflow_menu_id, 3, 'todo', 'oa/workflow/task/todo', 1, 0, 'C', '0', '0', 'oa:workflow:task:todo', 'time', 'admin', sysdate(), 'admin', sysdate(), '待办任务'),
('已办任务', @workflow_menu_id, 4, 'done', 'oa/workflow/task/done', 1, 0, 'C', '0', '0', 'oa:workflow:task:done', 'checkbox', 'admin', sysdate(), 'admin', sysdate(), '已办任务');

-- 公文管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('收文管理', @document_menu_id, 1, 'receive', 'oa/document/receive/index', 1, 0, 'C', '0', '0', 'oa:document:receive:list', 'download', 'admin', sysdate(), 'admin', sysdate(), '收文管理'),
('发文管理', @document_menu_id, 2, 'send', 'oa/document/send/index', 1, 0, 'C', '0', '0', 'oa:document:send:list', 'upload', 'admin', sysdate(), 'admin', sysdate(), '发文管理'),
('文档模板', @document_menu_id, 3, 'template', 'oa/document/template/index', 1, 0, 'C', '0', '0', 'oa:document:template:list', 'form', 'admin', sysdate(), 'admin', sysdate(), '文档模板管理');

-- 个人办公子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('个人信息', @personal_menu_id, 1, 'profile', 'oa/personal/profile/index', 1, 0, 'C', '0', '0', 'oa:personal:profile:view', 'user', 'admin', sysdate(), 'admin', sysdate(), '个人信息管理'),
('日程管理', @personal_menu_id, 2, 'schedule', 'oa/personal/schedule/index', 1, 0, 'C', '0', '0', 'oa:personal:schedule:list', 'date', 'admin', sysdate(), 'admin', sysdate(), '日程管理'),
('通讯录', @personal_menu_id, 3, 'contact', 'oa/personal/contact/index', 1, 0, 'C', '0', '0', 'oa:personal:contact:list', 'phone', 'admin', sysdate(), 'admin', sysdate(), '通讯录管理'),
('工作报告', @personal_menu_id, 4, 'report', 'oa/personal/report/index', 1, 0, 'C', '0', '0', 'oa:personal:report:list', 'edit', 'admin', sysdate(), 'admin', sysdate(), '工作报告');

-- 会议管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('会议室管理', @meeting_menu_id, 1, 'room', 'oa/meeting/room/index', 1, 0, 'C', '0', '0', 'oa:meeting:room:list', 'house', 'admin', sysdate(), 'admin', sysdate(), '会议室管理'),
('会议管理', @meeting_menu_id, 2, 'meeting', 'oa/meeting/meeting/index', 1, 0, 'C', '0', '0', 'oa:meeting:meeting:list', 'peoples', 'admin', sysdate(), 'admin', sysdate(), '会议管理');

-- 印章管理子菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('印章证照', @seal_menu_id, 1, 'certificate', 'oa/seal/certificate/index', 1, 0, 'C', '0', '0', 'oa:seal:certificate:list', 'stamp', 'admin', sysdate(), 'admin', sysdate(), '印章证照管理'),
('用印申请', @seal_menu_id, 2, 'application', 'oa/seal/application/index', 1, 0, 'C', '0', '0', 'oa:seal:application:list', 'form', 'admin', sysdate(), 'admin', sysdate(), '用印申请管理');

-- 为管理员角色分配OA菜单权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE menu_name LIKE '%OA%' OR path LIKE '%oa%' OR parent_id IN (
    SELECT menu_id FROM sys_menu WHERE menu_name = 'OA办公'
);

-- 添加按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
-- 工作流定义按钮权限
('流程定义查询', (SELECT menu_id FROM sys_menu WHERE menu_name = '流程定义' AND parent_id = @workflow_menu_id), 1, '#', '', 1, 0, 'F', '0', '0', 'oa:workflow:definition:query', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('流程定义新增', (SELECT menu_id FROM sys_menu WHERE menu_name = '流程定义' AND parent_id = @workflow_menu_id), 2, '#', '', 1, 0, 'F', '0', '0', 'oa:workflow:definition:add', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('流程定义修改', (SELECT menu_id FROM sys_menu WHERE menu_name = '流程定义' AND parent_id = @workflow_menu_id), 3, '#', '', 1, 0, 'F', '0', '0', 'oa:workflow:definition:edit', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('流程定义删除', (SELECT menu_id FROM sys_menu WHERE menu_name = '流程定义' AND parent_id = @workflow_menu_id), 4, '#', '', 1, 0, 'F', '0', '0', 'oa:workflow:definition:remove', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('流程部署', (SELECT menu_id FROM sys_menu WHERE menu_name = '流程定义' AND parent_id = @workflow_menu_id), 5, '#', '', 1, 0, 'F', '0', '0', 'oa:workflow:deploy', '#', 'admin', sysdate(), 'admin', sysdate(), ''),

-- 收文管理按钮权限
('收文查询', (SELECT menu_id FROM sys_menu WHERE menu_name = '收文管理' AND parent_id = @document_menu_id), 1, '#', '', 1, 0, 'F', '0', '0', 'oa:document:receive:query', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('收文新增', (SELECT menu_id FROM sys_menu WHERE menu_name = '收文管理' AND parent_id = @document_menu_id), 2, '#', '', 1, 0, 'F', '0', '0', 'oa:document:receive:add', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('收文修改', (SELECT menu_id FROM sys_menu WHERE menu_name = '收文管理' AND parent_id = @document_menu_id), 3, '#', '', 1, 0, 'F', '0', '0', 'oa:document:receive:edit', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('收文删除', (SELECT menu_id FROM sys_menu WHERE menu_name = '收文管理' AND parent_id = @document_menu_id), 4, '#', '', 1, 0, 'F', '0', '0', 'oa:document:receive:remove', '#', 'admin', sysdate(), 'admin', sysdate(), ''),

-- 个人日程按钮权限
('日程查询', (SELECT menu_id FROM sys_menu WHERE menu_name = '日程管理' AND parent_id = @personal_menu_id), 1, '#', '', 1, 0, 'F', '0', '0', 'oa:personal:schedule:query', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('日程新增', (SELECT menu_id FROM sys_menu WHERE menu_name = '日程管理' AND parent_id = @personal_menu_id), 2, '#', '', 1, 0, 'F', '0', '0', 'oa:personal:schedule:add', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('日程修改', (SELECT menu_id FROM sys_menu WHERE menu_name = '日程管理' AND parent_id = @personal_menu_id), 3, '#', '', 1, 0, 'F', '0', '0', 'oa:personal:schedule:edit', '#', 'admin', sysdate(), 'admin', sysdate(), ''),
('日程删除', (SELECT menu_id FROM sys_menu WHERE menu_name = '日程管理' AND parent_id = @personal_menu_id), 4, '#', '', 1, 0, 'F', '0', '0', 'oa:personal:schedule:remove', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 为管理员角色分配所有新增的权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE menu_id NOT IN (SELECT menu_id FROM sys_role_menu WHERE role_id = 1)
AND (menu_name LIKE '%OA%' OR path LIKE '%oa%' OR perms LIKE 'oa:%' OR parent_id IN (
    SELECT DISTINCT parent_id FROM sys_menu WHERE perms LIKE 'oa:%'
));

COMMIT;
