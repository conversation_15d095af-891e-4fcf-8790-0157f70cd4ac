-- OA系统数据库表初始化脚本

-- 删除已存在的OA表
DROP TABLE IF EXISTS oa_personal_schedule;
DROP TABLE IF EXISTS oa_personal_contact;
DROP TABLE IF EXISTS oa_work_report;
DROP TABLE IF EXISTS oa_document_receive;
DROP TABLE IF EXISTS oa_document_send;
DROP TABLE IF EXISTS oa_document_template;
DROP TABLE IF EXISTS oa_meeting_room;
DROP TABLE IF EXISTS oa_meeting;
DROP TABLE IF EXISTS oa_seal_certificate;
DROP TABLE IF EXISTS oa_seal_application;
DROP TABLE IF EXISTS oa_workflow_definition;
DROP TABLE IF EXISTS oa_workflow_instance;

-- 个人日程表
CREATE TABLE oa_personal_schedule (
    schedule_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日程ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(50) COMMENT '用户姓名',
    title VARCHAR(200) NOT NULL COMMENT '日程标题',
    content TEXT COMMENT '日程内容',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    location VARCHAR(200) COMMENT '地点',
    remind_time DATETIME COMMENT '提醒时间',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1待开始2进行中3已完成4已取消)',
    repeat_type CHAR(1) DEFAULT '0' COMMENT '重复类型(0不重复1每天2每周3每月4每年)',
    priority CHAR(1) DEFAULT '1' COMMENT '重要程度(1普通2重要3紧急)',
    participants VARCHAR(500) COMMENT '参与人员',
    is_all_day CHAR(1) DEFAULT '0' COMMENT '是否全天(0否1是)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='个人日程表';

-- 个人通讯录表
CREATE TABLE oa_personal_contact (
    contact_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '通讯录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    contact_name VARCHAR(100) NOT NULL COMMENT '联系人姓名',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '邮箱地址',
    contact_company VARCHAR(200) COMMENT '公司名称',
    contact_position VARCHAR(100) COMMENT '职位',
    contact_address VARCHAR(500) COMMENT '联系地址',
    contact_group VARCHAR(50) COMMENT '分组',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1正常)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='个人通讯录表';

-- 工作报告表
CREATE TABLE oa_work_report (
    report_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报告ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(50) COMMENT '用户姓名',
    report_title VARCHAR(200) NOT NULL COMMENT '报告标题',
    report_type CHAR(1) DEFAULT '1' COMMENT '报告类型(1日报2周报3月报4年报)',
    report_date DATE NOT NULL COMMENT '报告日期',
    work_content TEXT COMMENT '工作内容',
    work_summary TEXT COMMENT '工作总结',
    next_plan TEXT COMMENT '下期计划',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1草稿2已提交3已审核)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='工作报告表';

-- 收文管理表
CREATE TABLE oa_document_receive (
    doc_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文档ID',
    doc_title VARCHAR(200) NOT NULL COMMENT '文档标题',
    doc_number VARCHAR(100) COMMENT '文档编号',
    source_unit VARCHAR(200) COMMENT '来文单位',
    receive_date DATE COMMENT '收文日期',
    doc_type VARCHAR(50) COMMENT '文档类型',
    urgency_level CHAR(1) DEFAULT '1' COMMENT '紧急程度(1普通2紧急3特急)',
    secret_level CHAR(1) DEFAULT '1' COMMENT '密级(1公开2内部3秘密4机密)',
    doc_content TEXT COMMENT '文档内容',
    attachment_path VARCHAR(500) COMMENT '附件路径',
    handler_id BIGINT COMMENT '处理人ID',
    handler_name VARCHAR(50) COMMENT '处理人姓名',
    handle_status CHAR(1) DEFAULT '1' COMMENT '处理状态(1待处理2处理中3已完成)',
    handle_opinion TEXT COMMENT '处理意见',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1正常)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='收文管理表';

-- 发文管理表
CREATE TABLE oa_document_send (
    doc_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文档ID',
    doc_title VARCHAR(200) NOT NULL COMMENT '文档标题',
    doc_number VARCHAR(100) COMMENT '文档编号',
    target_unit VARCHAR(200) COMMENT '发文单位',
    send_date DATE COMMENT '发文日期',
    doc_type VARCHAR(50) COMMENT '文档类型',
    urgency_level CHAR(1) DEFAULT '1' COMMENT '紧急程度(1普通2紧急3特急)',
    secret_level CHAR(1) DEFAULT '1' COMMENT '密级(1公开2内部3秘密4机密)',
    doc_content TEXT COMMENT '文档内容',
    attachment_path VARCHAR(500) COMMENT '附件路径',
    drafter_id BIGINT COMMENT '起草人ID',
    drafter_name VARCHAR(50) COMMENT '起草人姓名',
    approve_status CHAR(1) DEFAULT '1' COMMENT '审批状态(1草稿2待审批3已审批4已发送)',
    approve_opinion TEXT COMMENT '审批意见',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1正常)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='发文管理表';

-- 文档模板表
CREATE TABLE oa_document_template (
    template_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '模板ID',
    template_name VARCHAR(200) NOT NULL COMMENT '模板名称',
    template_type VARCHAR(50) COMMENT '模板类型',
    template_content TEXT COMMENT '模板内容',
    template_path VARCHAR(500) COMMENT '模板文件路径',
    is_default CHAR(1) DEFAULT '0' COMMENT '是否默认(0否1是)',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1正常)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='文档模板表';

-- 会议室表
CREATE TABLE oa_meeting_room (
    room_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '会议室ID',
    room_name VARCHAR(100) NOT NULL COMMENT '会议室名称',
    room_location VARCHAR(200) COMMENT '会议室位置',
    room_capacity INT COMMENT '容纳人数',
    room_equipment TEXT COMMENT '设备配置',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1正常)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='会议室表';

-- 会议管理表
CREATE TABLE oa_meeting (
    meeting_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '会议ID',
    meeting_title VARCHAR(200) NOT NULL COMMENT '会议标题',
    meeting_type VARCHAR(50) COMMENT '会议类型',
    room_id BIGINT COMMENT '会议室ID',
    room_name VARCHAR(100) COMMENT '会议室名称',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    organizer_id BIGINT COMMENT '组织者ID',
    organizer_name VARCHAR(50) COMMENT '组织者姓名',
    participants TEXT COMMENT '参会人员',
    meeting_agenda TEXT COMMENT '会议议程',
    meeting_content TEXT COMMENT '会议内容',
    meeting_summary TEXT COMMENT '会议纪要',
    status CHAR(1) DEFAULT '1' COMMENT '状态(1待开始2进行中3已结束4已取消)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='会议管理表';

-- 印章证照表
CREATE TABLE oa_seal_certificate (
    cert_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '证照ID',
    cert_name VARCHAR(200) NOT NULL COMMENT '证照名称',
    cert_type VARCHAR(50) COMMENT '证照类型',
    cert_number VARCHAR(100) COMMENT '证照编号',
    issue_unit VARCHAR(200) COMMENT '发证单位',
    issue_date DATE COMMENT '发证日期',
    expire_date DATE COMMENT '到期日期',
    cert_image_path VARCHAR(500) COMMENT '证照图片路径',
    keeper_id BIGINT COMMENT '保管人ID',
    keeper_name VARCHAR(50) COMMENT '保管人姓名',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1正常)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='印章证照表';

-- 用印申请表
CREATE TABLE oa_seal_application (
    apply_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '申请ID',
    apply_title VARCHAR(200) NOT NULL COMMENT '申请标题',
    cert_id BIGINT COMMENT '印章证照ID',
    cert_name VARCHAR(200) COMMENT '印章证照名称',
    apply_reason TEXT COMMENT '申请原因',
    use_purpose VARCHAR(500) COMMENT '使用用途',
    apply_date DATE COMMENT '申请日期',
    expect_date DATE COMMENT '预期使用日期',
    applicant_id BIGINT COMMENT '申请人ID',
    applicant_name VARCHAR(50) COMMENT '申请人姓名',
    approve_status CHAR(1) DEFAULT '1' COMMENT '审批状态(1待审批2已审批3已拒绝)',
    approve_opinion TEXT COMMENT '审批意见',
    approver_id BIGINT COMMENT '审批人ID',
    approver_name VARCHAR(50) COMMENT '审批人姓名',
    approve_time DATETIME COMMENT '审批时间',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1正常)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用印申请表';

-- 工作流定义表
CREATE TABLE oa_workflow_definition (
    workflow_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '工作流ID',
    workflow_name VARCHAR(200) NOT NULL COMMENT '工作流名称',
    workflow_key VARCHAR(100) NOT NULL COMMENT '工作流标识',
    workflow_version INT DEFAULT 1 COMMENT '版本号',
    description TEXT COMMENT '描述',
    bpmn_xml TEXT COMMENT 'BPMN XML内容',
    deployment_id VARCHAR(100) COMMENT '部署ID',
    process_definition_id VARCHAR(100) COMMENT '流程定义ID',
    status CHAR(1) DEFAULT '1' COMMENT '状态(0停用1正常)',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='工作流定义表';

-- 插入测试数据
INSERT INTO oa_personal_schedule (user_id, user_name, title, content, start_time, end_time, location, status, create_by, create_time) VALUES
(1, 'admin', '项目会议', '讨论OA系统开发进度', '2025-07-01 09:00:00', '2025-07-01 10:30:00', '会议室A', '1', 'admin', NOW()),
(1, 'admin', '客户拜访', '拜访重要客户', '2025-07-01 14:00:00', '2025-07-01 16:00:00', '客户公司', '1', 'admin', NOW()),
(1, 'admin', '团队建设', '部门团建活动', '2025-07-02 10:00:00', '2025-07-02 18:00:00', '户外拓展基地', '1', 'admin', NOW());

INSERT INTO oa_document_receive (doc_title, doc_number, source_unit, receive_date, doc_type, doc_content, handle_status, create_by, create_time) VALUES
('关于系统升级的通知', '*********', '信息技术部', '2025-07-01', '通知', '系统将于本周末进行升级维护', '1', 'admin', NOW()),
('年度工作计划', '*********', '人力资源部', '2025-07-01', '计划', '2025年度工作计划安排', '1', 'admin', NOW());

INSERT INTO oa_meeting_room (room_name, room_location, room_capacity, room_equipment, create_by, create_time) VALUES
('会议室A', '办公楼3楼', 20, '投影仪、音响、白板', 'admin', NOW()),
('会议室B', '办公楼5楼', 50, '投影仪、音响、白板、视频会议设备', 'admin', NOW()),
('小会议室', '办公楼2楼', 8, '白板、电话会议设备', 'admin', NOW());

COMMIT;
