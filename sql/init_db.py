#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
用于执行OA系统的数据库表创建和菜单初始化
"""

import pymysql
import sys
import os

# 数据库连接配置
DB_CONFIG = {
    'host': 'rm-hp389tgow194675569o.mysql.huhehaote.rds.aliyuncs.com',
    'port': 3306,
    'user': 'base',
    'password': 'base_123',
    'database': 'base-oa',
    'charset': 'utf8mb4'
}

def execute_sql_file(cursor, file_path):
    """执行SQL文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句（以分号分隔）
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        for sql in sql_statements:
            if sql:
                print(f"执行SQL: {sql[:100]}...")
                cursor.execute(sql)
        
        print(f"成功执行SQL文件: {file_path}")
        return True
    except Exception as e:
        print(f"执行SQL文件失败 {file_path}: {str(e)}")
        return False

def main():
    """主函数"""
    try:
        # 连接数据库
        print("连接数据库...")
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 获取当前脚本目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 执行表初始化脚本
        tables_sql = os.path.join(script_dir, 'oa_tables_init.sql')
        if os.path.exists(tables_sql):
            print("执行数据库表初始化...")
            if execute_sql_file(cursor, tables_sql):
                connection.commit()
                print("数据库表初始化完成")
            else:
                connection.rollback()
                print("数据库表初始化失败")
                return False
        
        # 执行菜单初始化脚本
        menu_sql = os.path.join(script_dir, 'oa_menu_init.sql')
        if os.path.exists(menu_sql):
            print("执行菜单初始化...")
            if execute_sql_file(cursor, menu_sql):
                connection.commit()
                print("菜单初始化完成")
            else:
                connection.rollback()
                print("菜单初始化失败")
                return False
        
        print("数据库初始化完成！")
        return True
        
    except Exception as e:
        print(f"数据库初始化失败: {str(e)}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
