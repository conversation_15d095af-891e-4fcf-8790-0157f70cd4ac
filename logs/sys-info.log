22:07:05.367 [restartedMain] INFO  c.b.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_412 on MacBookPro with PID 6702 (/Users/<USER>/IDEA workspace/bfsd/oa-hs/ruoyi-admin/target/classes started by maoliang in /Users/<USER>/IDEA workspace/bfsd/oa-hs/ruoyi-admin)
22:07:05.368 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
22:07:05.369 [restartedMain] INFO  c.b.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:07:07.277 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
22:07:07.277 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:07:07.278 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
22:07:07.334 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:07:07.781 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
22:08:12.183 [restartedMain] INFO  c.b.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_412 on MacBookPro with PID 6831 (/Users/<USER>/IDEA workspace/bfsd/oa-hs/ruoyi-admin/target/classes started by maoliang in /Users/<USER>/IDEA workspace/bfsd/oa-hs/ruoyi-admin)
22:08:12.185 [restartedMain] INFO  c.b.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:08:12.185 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
22:08:14.075 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
22:08:14.076 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:08:14.076 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
22:08:14.134 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:08:15.134 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-0} closing ...
22:08:15.148 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
22:11:13.373 [restartedMain] INFO  c.b.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_412 on MacBookPro with PID 7203 (/Users/<USER>/IDEA workspace/bfsd/oa-hs/ruoyi-admin/target/classes started by maoliang in /Users/<USER>/IDEA workspace/bfsd/oa-hs/ruoyi-admin)
22:11:13.375 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
22:11:13.376 [restartedMain] INFO  c.b.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:11:15.309 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
22:11:15.309 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:11:15.309 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
22:11:15.369 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:11:20.272 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1} inited
22:11:21.256 [restartedMain] INFO  o.f.e.i.c.StandaloneProcessEngineConfiguration - [initConfigurators,1073] - Found 2 Engine Configurators in total:
22:11:21.257 [restartedMain] INFO  o.f.e.i.c.StandaloneProcessEngineConfiguration - [initConfigurators,1075] - class org.flowable.eventregistry.impl.configurator.EventRegistryEngineConfigurator (priority:100000)
22:11:21.257 [restartedMain] INFO  o.f.e.i.c.StandaloneProcessEngineConfiguration - [initConfigurators,1075] - class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
22:11:21.258 [restartedMain] INFO  o.f.e.i.c.StandaloneProcessEngineConfiguration - [configuratorsBeforeInit,1101] - Executing beforeInit() of class org.flowable.eventregistry.impl.configurator.EventRegistryEngineConfigurator (priority:100000)
22:11:21.262 [restartedMain] INFO  o.f.e.i.c.StandaloneProcessEngineConfiguration - [configuratorsBeforeInit,1101] - Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
22:11:21.898 [restartedMain] INFO  o.f.c.e.i.a.DefaultAsyncTaskExecutor - [initializeExecutor,154] - Creating thread pool queue of size 2048
22:11:21.899 [restartedMain] INFO  o.f.c.e.i.a.DefaultAsyncTaskExecutor - [initializeExecutor,159] - Creating thread factory with naming pattern flowable-async-job-executor-thread-%d
22:11:21.900 [restartedMain] INFO  o.f.c.e.i.a.DefaultAsyncTaskExecutor - [initializeExecutor,164] - Creating executor service with corePoolSize 8, maxPoolSize 8 and keepAliveTime 5000
22:11:21.901 [restartedMain] INFO  o.f.e.i.c.StandaloneProcessEngineConfiguration - [configuratorsAfterInit,1108] - Executing configure() of class org.flowable.eventregistry.impl.configurator.EventRegistryEngineConfigurator (priority:100000)
22:11:23.972 [restartedMain] INFO  o.f.c.e.i.d.CommonDbSchemaManager - [executeSchemaResource,236] - performing create on common with resource org/flowable/common/db/create/flowable.mysql.create.common.sql
22:11:23.973 [restartedMain] INFO  o.f.c.e.i.d.CommonDbSchemaManager - [executeSchemaResource,252] - Found MySQL: majorVersion=5 minorVersion=7
22:11:27.697 [restartedMain] INFO  l.lockservice - [log,23] - Successfully acquired change log lock
22:11:28.207 [restartedMain] INFO  liquibase.changelog - [log,23] - Creating database history table with name: `base-oa`.FLW_EV_DATABASECHANGELOG
22:11:28.765 [restartedMain] INFO  liquibase.changelog - [log,23] - Reading from `base-oa`.FLW_EV_DATABASECHANGELOG
22:11:30.209 [restartedMain] INFO  l.lockservice - [log,23] - Successfully released change log lock
22:11:30.359 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-1} closing ...
22:11:30.370 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2247] - {dataSource-1} closed
22:11:30.506 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
22:36:39.762 [restartedMain] INFO  c.b.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_412 on MacBookPro with PID 10096 (/Users/<USER>/IDEA workspace/bfsd/oa-hs/ruoyi-admin/target/classes started by maoliang in /Users/<USER>/IDEA workspace/bfsd/oa-hs/ruoyi-admin)
22:36:39.765 [restartedMain] INFO  c.b.RuoYiApplication - [logStartupProfileInfo,686] - The following 2 profiles are active: "druid", "oa"
22:36:39.765 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
22:36:41.601 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
22:36:41.602 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
22:36:41.602 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.75]
22:36:41.664 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
22:36:46.207 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1} inited
22:36:47.799 [restartedMain] INFO  o.f.s.b.c.CmmnEngineAutoConfiguration - [discoverDeploymentResources,104] - No deployment resources were found for autodeployment
22:36:48.053 [restartedMain] INFO  o.f.s.b.d.DmnEngineAutoConfiguration - [discoverDeploymentResources,104] - No deployment resources were found for autodeployment
22:36:48.151 [restartedMain] INFO  o.f.s.b.f.FormEngineAutoConfiguration - [discoverDeploymentResources,104] - No deployment resources were found for autodeployment
22:36:48.225 [restartedMain] INFO  o.f.s.b.e.EventRegistryAutoConfiguration - [discoverDeploymentResources,104] - No deployment resources were found for autodeployment
22:36:48.297 [restartedMain] INFO  o.f.s.b.a.AppEngineAutoConfiguration - [discoverDeploymentResources,104] - No deployment resources were found for autodeployment
22:36:48.350 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [initConfigurators,1073] - Found 7 Engine Configurators in total:
22:36:48.351 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [initConfigurators,1075] - class org.flowable.engine.spring.configurator.SpringProcessEngineConfigurator (priority:50000)
22:36:48.351 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [initConfigurators,1075] - class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
22:36:48.351 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [initConfigurators,1075] - class org.flowable.idm.spring.configurator.SpringIdmEngineConfigurator (priority:150000)
22:36:48.351 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [initConfigurators,1075] - class org.flowable.dmn.spring.configurator.SpringDmnEngineConfigurator (priority:200000)
22:36:48.351 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [initConfigurators,1075] - class org.flowable.form.spring.configurator.SpringFormEngineConfigurator (priority:300000)
22:36:48.351 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [initConfigurators,1075] - class org.flowable.content.spring.configurator.SpringContentEngineConfigurator (priority:400000)
22:36:48.352 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [initConfigurators,1075] - class org.flowable.cmmn.spring.configurator.SpringCmmnEngineConfigurator (priority:500000)
22:36:48.352 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsBeforeInit,1101] - Executing beforeInit() of class org.flowable.engine.spring.configurator.SpringProcessEngineConfigurator (priority:50000)
22:36:48.354 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsBeforeInit,1101] - Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
22:36:48.356 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsBeforeInit,1101] - Executing beforeInit() of class org.flowable.idm.spring.configurator.SpringIdmEngineConfigurator (priority:150000)
22:36:48.358 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsBeforeInit,1101] - Executing beforeInit() of class org.flowable.dmn.spring.configurator.SpringDmnEngineConfigurator (priority:200000)
22:36:48.360 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsBeforeInit,1101] - Executing beforeInit() of class org.flowable.form.spring.configurator.SpringFormEngineConfigurator (priority:300000)
22:36:48.362 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsBeforeInit,1101] - Executing beforeInit() of class org.flowable.content.spring.configurator.SpringContentEngineConfigurator (priority:400000)
22:36:48.364 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsBeforeInit,1101] - Executing beforeInit() of class org.flowable.cmmn.spring.configurator.SpringCmmnEngineConfigurator (priority:500000)
22:36:48.975 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsAfterInit,1108] - Executing configure() of class org.flowable.engine.spring.configurator.SpringProcessEngineConfigurator (priority:50000)
22:36:55.228 [restartedMain] INFO  o.f.e.i.ProcessEngineImpl - [<init>,89] - ProcessEngine default created
22:36:55.330 [restartedMain] INFO  o.f.e.i.c.ValidateV5EntitiesCmd - [execute,43] - Total of v5 deployments found: 0
22:36:55.764 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsAfterInit,1108] - Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
22:36:57.104 [restartedMain] INFO  l.lockservice - [log,23] - Successfully acquired change log lock
22:36:58.414 [restartedMain] INFO  liquibase.changelog - [log,23] - Reading from `base-oa`.FLW_EV_DATABASECHANGELOG
22:36:58.635 [restartedMain] INFO  l.lockservice - [log,23] - Successfully released change log lock
22:36:58.719 [restartedMain] INFO  o.f.e.i.EventRegistryEngineImpl - [<init>,53] - EventRegistryEngine default created
22:36:58.723 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsAfterInit,1108] - Executing configure() of class org.flowable.idm.spring.configurator.SpringIdmEngineConfigurator (priority:150000)
22:36:59.513 [restartedMain] INFO  o.f.i.e.i.IdmEngineImpl - [<init>,52] - IdmEngine default created
22:36:59.517 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsAfterInit,1108] - Executing configure() of class org.flowable.dmn.spring.configurator.SpringDmnEngineConfigurator (priority:200000)
22:37:00.628 [restartedMain] INFO  l.lockservice - [log,23] - Successfully acquired change log lock
22:37:01.654 [restartedMain] INFO  liquibase.changelog - [log,23] - Reading from `base-oa`.ACT_DMN_DATABASECHANGELOG
22:37:01.838 [restartedMain] INFO  l.lockservice - [log,23] - Successfully released change log lock
22:37:01.922 [restartedMain] INFO  o.f.d.e.i.DmnEngineImpl - [<init>,55] - DmnEngine default created
22:37:01.925 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsAfterInit,1108] - Executing configure() of class org.flowable.form.spring.configurator.SpringFormEngineConfigurator (priority:300000)
22:37:03.000 [restartedMain] INFO  l.lockservice - [log,23] - Successfully acquired change log lock
22:37:04.010 [restartedMain] INFO  liquibase.changelog - [log,23] - Reading from `base-oa`.ACT_FO_DATABASECHANGELOG
22:37:04.198 [restartedMain] INFO  l.lockservice - [log,23] - Successfully released change log lock
22:37:04.283 [restartedMain] INFO  o.f.f.e.i.FormEngineImpl - [<init>,52] - FormEngine default created
22:37:04.287 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsAfterInit,1108] - Executing configure() of class org.flowable.content.spring.configurator.SpringContentEngineConfigurator (priority:400000)
22:37:04.292 [restartedMain] INFO  o.f.c.s.SpringContentEngineConfiguration - [initContentStorage,196] - Content file system root : /Users/<USER>/content
22:37:04.731 [restartedMain] INFO  l.lockservice - [log,23] - Successfully acquired change log lock
22:37:05.750 [restartedMain] INFO  liquibase.changelog - [log,23] - Reading from `base-oa`.ACT_CO_DATABASECHANGELOG
22:37:05.923 [restartedMain] INFO  l.lockservice - [log,23] - Successfully released change log lock
22:37:06.006 [restartedMain] INFO  o.f.c.e.i.ContentEngineImpl - [<init>,48] - ContentEngine default created
22:37:06.009 [restartedMain] INFO  o.f.a.s.SpringAppEngineConfiguration - [configuratorsAfterInit,1108] - Executing configure() of class org.flowable.cmmn.spring.configurator.SpringCmmnEngineConfigurator (priority:500000)
22:37:11.629 [restartedMain] INFO  l.lockservice - [log,23] - Successfully acquired change log lock
22:37:13.364 [restartedMain] INFO  liquibase.changelog - [log,23] - Reading from `base-oa`.ACT_CMMN_DATABASECHANGELOG
22:37:13.555 [restartedMain] INFO  l.lockservice - [log,23] - Successfully released change log lock
22:37:13.638 [restartedMain] INFO  o.f.c.e.i.CmmnEngineImpl - [<init>,72] - CmmnEngine default created
22:37:15.986 [restartedMain] INFO  l.lockservice - [log,23] - Successfully acquired change log lock
22:37:17.063 [restartedMain] INFO  liquibase.changelog - [log,23] - Reading from `base-oa`.ACT_APP_DATABASECHANGELOG
22:37:17.257 [restartedMain] INFO  l.lockservice - [log,23] - Successfully released change log lock
22:37:17.340 [restartedMain] INFO  o.f.a.e.i.AppEngineImpl - [<init>,48] - AppEngine default created
22:37:17.918 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
22:37:17.926 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
22:37:17.926 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
22:37:17.927 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
22:37:17.927 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

22:37:17.928 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
22:37:17.928 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
22:37:17.928 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6355db29
22:37:19.474 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
22:37:19.490 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,120] - Context refreshed
22:37:19.503 [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - [start,123] - Found 1 custom documentation plugin(s)
22:37:19.517 [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - [scan,41] - Scanning for api listing references
22:37:19.620 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: buildingProgressUsingGET_1
22:37:19.681 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_1
22:37:19.705 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPUT_1
22:37:19.709 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_1
22:37:19.711 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_1
22:37:19.714 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importDataUsingPOST_1
22:37:19.717 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_2
22:37:19.720 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingDELETE_1
22:37:19.727 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: addUsingPOST_1
22:37:19.730 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: editUsingPUT_2
22:37:19.733 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: exportUsingPOST_2
22:37:19.735 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: getInfoUsingGET_2
22:37:19.738 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importDataUsingPOST_2
22:37:19.740 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_1
22:37:19.743 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: listUsingGET_3
22:37:19.745 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: removeUsingDELETE_2
22:37:19.748 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importDataUsingPOST_3
22:37:19.750 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_2
22:37:19.752 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importDataUsingPOST_4
22:37:19.755 [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - [startingWith,40] - Generating unique operation named: importTemplateUsingPOST_3
22:37:19.783 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
22:37:19.795 [restartedMain] INFO  c.b.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 40.373 seconds (JVM running for 40.816)
22:39:00.808 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:39:05.174 [http-nio-8080-exec-4] INFO  c.b.f.w.s.UserDetailsServiceImpl - [loadUserByUsername,43] - 登录用户：18890062616 不存在.
22:39:05.218 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[18890062616][Error][用户不存在/密码错误]
22:39:09.666 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Error][验证码错误]
22:39:13.013 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Success][登录成功]
